<context>
# Overview
Integrate Composio tool-calling into Atlas to enable seamless, native access to 100+ external APIs and tools (e.g., GitHub, Gmail, Salesforce) via agent chat. This will allow users to connect integrations, authenticate, and execute actions directly from the Atlas UI, without leaving the app. The goal is to provide a unified, secure, and user-friendly automation experience for all users.

# Core Features
- Tool-calling via Composio in agent chat (OpenAI tool-calling compatible)
- Integration management UI (connect/disconnect integrations, view status)
- OAuth and API key flows handled natively in-app (no external redirects)
- Dynamic action forms for each integration/tool
- Real-time feedback/results in chat
- Secure credential storage and management

# User Experience
- User can browse and connect integrations from a dedicated UI section
- When a tool call is needed, user is prompted to connect if not already done
- All auth flows (OAuth/API key) are handled in-app
- Users see results of tool calls directly in chat
- Clear error handling and guidance for failed actions
</context>
<PRD>
# Technical Architecture
- Backend: Python, composio-openai SDK, OpenAI API, secure credential proxying
- Frontend: Next.js/React, dynamic integration UI, chat interface
- APIs: Composio tool and auth endpoints, OpenAI chat completions
- Infrastructure: No self-hosting required, uses Composio SaaS

# Development Roadmap
- Phase 1: Backend integration with composio-openai SDK, tool fetching, tool call handling
- Phase 2: Frontend UI for integration management and auth flows
- Phase 3: Dynamic action forms and chat integration
- Phase 4: Error handling, user feedback, and polish
- Phase 5: Testing and documentation

# Logical Dependency Chain
- Backend SDK setup and tool call plumbing
- Integration management UI and auth flow support
- Dynamic action forms and chat result display
- Error handling and polish

# Risks and Mitigations
- Risk: OAuth flow complexity — Mitigation: Use Composio's built-in UI and callback support
- Risk: Tool coverage — Mitigation: Start with most popular integrations, expand as needed
- Risk: User confusion — Mitigation: Clear UI/UX and error messages

# Appendix
- Composio docs: https://docs.composio.dev/tool-calling/introduction
- OpenAI tool-calling: https://platform.openai.com/docs/guides/function-calling
</PRD>
