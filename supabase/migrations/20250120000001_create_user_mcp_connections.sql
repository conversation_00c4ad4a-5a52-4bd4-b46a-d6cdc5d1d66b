-- Create user_mcp_connections table for per-user MCP authentication
CREATE TABLE user_mcp_connections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    service_name TEXT NOT NULL, -- 'gmail', 'google_drive', 'notion', etc.
    composio_connection_id TEXT UNIQUE NOT NULL,
    composio_entity_id TEXT NOT NULL, -- Composio's entity_id (same as user_id)
    mcp_server_id TEXT, -- Composio MCP server ID
    mcp_url TEXT, -- Full MCP URL with user_id parameter
    status TEXT DEFAULT 'pending_auth', -- 'active', 'pending_auth', 'error', 'disconnected'
    auth_config_id TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    -- Ensure one connection per user per service
    UNIQUE(user_id, service_name)
);

-- Enable Row Level Security
ALTER TABLE user_mcp_connections ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only access their own connections
CREATE POLICY "Users can only access their own connections"
ON user_mcp_connections
FOR ALL
USING (auth.uid() = user_id);

-- Create indexes for performance
CREATE INDEX idx_user_mcp_connections_user_id ON user_mcp_connections(user_id);
CREATE INDEX idx_user_mcp_connections_service ON user_mcp_connections(service_name);
CREATE INDEX idx_user_mcp_connections_status ON user_mcp_connections(status);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_mcp_connections_updated_at
    BEFORE UPDATE ON user_mcp_connections
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
