-- Update user_mcp_connections table for improved Composio integration
-- Adds columns for connection management based on Composio documentation

-- Add columns for better connection management
ALTER TABLE user_mcp_connections
ADD COLUMN IF NOT EXISTS connected_account_id TEXT, -- Specific connection ID for multi-account support
ADD COLUMN IF NOT EXISTS expires_at TIMESTAMPTZ, -- Token expiration time
ADD COLUMN IF NOT EXISTS refresh_token TEXT, -- Token for refreshing access
ADD COLUMN IF NOT EXISTS last_verified_at TIMESTAMPTZ, -- Last verification timestamp
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE, -- Active status flag
ADD COLUMN IF NOT EXISTS error_message TEXT; -- Store error messages

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_user_mcp_connections_entity_id 
    ON user_mcp_connections(composio_entity_id);
    
CREATE INDEX IF NOT EXISTS idx_user_mcp_connections_connected_account 
    ON user_mcp_connections(connected_account_id);

-- Update existing connections to set is_active based on status
UPDATE user_mcp_connections
SET is_active = (status = 'active')
WHERE is_active IS NULL;

-- Set last_verified_at for existing connections
UPDATE user_mcp_connections
SET last_verified_at = updated_at
WHERE last_verified_at IS NULL;

-- Comments for future reference
COMMENT ON COLUMN user_mcp_connections.connected_account_id IS 'Specific Composio connected account ID for multi-account support';
COMMENT ON COLUMN user_mcp_connections.expires_at IS 'When the access token expires';
COMMENT ON COLUMN user_mcp_connections.refresh_token IS 'Token used to refresh expired access tokens';
COMMENT ON COLUMN user_mcp_connections.last_verified_at IS 'When the connection status was last verified with Composio';
COMMENT ON COLUMN user_mcp_connections.is_active IS 'Whether the connection is currently active';
COMMENT ON COLUMN user_mcp_connections.error_message IS 'Latest error message if connection failed';
