# PicaOS Integration Research & Notes

_Last updated: 2025-05-13_

---

## 1. Overview
PicaOS (Pica) is an open-source platform for building, deploying, and scaling AI agents. It provides:
- Unified API access to 100+ integrations (QuickBooks, Salesforce, HubSpot, Shopify, etc.)
- Multiple integration options: REST API, Python SDK (LangChain), Vercel AI SDK, MCP (Anthropic), and more

---

## 2. Authentication
- All API requests require an API key via the `x-pica-secret` header.
- Keys are managed in the Pica dashboard.
- Two environments: Sandbox (testing) and Production (live). Keys are not interchangeable.

```http
x-pica-secret: YOUR_API_KEY
```

---

## 3. API Reference & Vault API
- Main API endpoint: https://api.picaos.com
- Vault API: manage connectors (list, delete, view connection details)
- Passthrough API: access 6000+ actions through a single endpoint
- Built-in authentication for connectors

---

## 4. Python SDK: LangChain
- Official SDK: `pica-langchain`
- Install:
```bash
pip install pica-langchain
```
- Usage Example:
```python
from langchain_openai import ChatOpenAI
from langchain.agents import AgentType
from pica_langchain import PicaClient, create_pica_agent, PicaClientOptions

pica_client = PicaClient(
    secret="YOUR_PICA_SECRET",
    options=PicaClientOptions(
        connectors=["*"]
    )
)

llm = ChatOpenAI(temperature=0, model="gpt-4o")

agent = create_pica_agent(
    client=pica_client,
    llm=llm,
    agent_type=AgentType.OPENAI_FUNCTIONS
)

result = agent.invoke({
    "input": "List my available google spreadsheets. ..."
})
print(f"Result: {result}")
```
- Configurable options: server_url, connectors, identity, identity_type, authkit

---

## 5. Vercel AI SDK
- JS/TS SDK for Vercel AI platform: `@picahq/ai`
- Not directly relevant for Python backend, but useful for frontend/JS apps

---

## 6. MCP (Model Control Protocol)
- Pica supports integration with Anthropic’s MCP for advanced agentic workflows
- Useful if you want to delegate tasks to Pica via MCP, but requires MCP-compatible infra

---

## 7. Integration Recommendations (for Python Backend)
- **Best fit:** Use the REST API directly or the Python SDK (`pica-langchain`) if you already use LangChain in your backend.
    - If your backend is not LangChain-based, use the REST API for maximum control and flexibility.
- **Workflow:**
    1. Store API key securely (env variable)
    2. Create a tool wrapper class (e.g., `PikaOSTool`) that handles auth, task submission, and result parsing
    3. (Optional) Implement webhook handler for async results if supported by Pica
    4. Register the tool in your ToolRegistry
- **Endpoints and payloads:** See official API reference for details. All requests need `x-pica-secret` header.

---

## 8. Useful Links
- [PicaOS Docs](https://docs.picaos.com/)
- [API Reference](https://docs.picaos.com/api-reference/introduction)
- [Authentication](https://docs.picaos.com/api-reference/authentication)
- [LangChain SDK](https://docs.picaos.com/sdk/langchain)
- [Vercel AI SDK](https://docs.picaos.com/sdk/vercel-ai)
- [MCP Integration](https://docs.picaos.com/sdk/anthropic-mcp)

---

## 9. TODO (Before Implementation)
- Review full API endpoint docs for payload/response structure
- Decide on REST API vs LangChain SDK (based on backend stack)
- Confirm if webhook/callback is needed for async tasks
- Document all endpoints and error cases in this file

---

_This file should be updated as integration progresses._


## 10. PicaOS Endpoint Details

### Passthrough API
- **Base URL:**
  ```
  https://api.picaos.com/v1/passthrough/{path}
  ```
  Where `{path}` is the endpoint path from the third-party API you want to access via Pica.
- **Required Headers:**
  - `x-pica-secret`: Your Pica API key
  - `x-pica-connection-key`: Connection key for the specific integration
- **Description:**
  - Lets you interact directly with the underlying API of any integration.
  - Responses are shaped to match the original API.

### Available Connectors
- **Endpoint:**
  ```
  GET https://api.picaos.com/v1/available-connectors
  Headers: x-pica-secret: YOUR_API_KEY
  ```
- **Description:**
  - Lists all connectors (integrations) available in PicaOS.
  - Each connector includes: name, key, platform, version, description, tags, oauth, etc.
- **Sample Response:**
  ```json
  {
    "type": "Available Connectors",
    "rows": [
      {
        "name": "ElevenLabs",
        "key": "api::elevenlabs::v1",
        "platform": "elevenlabs",
        "platformVersion": "v1",
        "description": "Create the most realistic speech with our AI audio platform",
        ...
      }
    ],
    "total": 66,
    "skip": 0,
    "limit": 20
  }
  ```
- **Query Parameters:**
  - `platform`, `authkit`, `key`, `name`, `category`, `limit`, `skip`

### Available Actions
- **Endpoint:**
  ```
  GET https://api.picaos.com/v1/available-actions/{platform}
  Headers: x-pica-secret: YOUR_API_KEY
  ```
- **Description:**
  - Lists all available actions for a given platform/connector.
  - Each action includes: title, key, method, platform
- **Sample Response:**
  ```json
  {
    "type": "Available Actions",
    "rows": [
      { "title": "Get Contents", "key": "getContents", "method": "POST", "platform": "exa" },
      { "title": "Search", "key": "search", "method": "POST", "platform": "exa" },
      ...
    ],
    "total": 4,
    "skip": 0,
    "limit": 20
  }
  ```
- **Path Parameters:**
  - `platform` (required)
- **Query Parameters:**
  - `title`, `key`, `method`, `limit`, `skip`

---
