# Scalable MCP Tool System Implementation Plan

## 📋 **Overview**

This document provides step-by-step instructions to implement a scalable MCP (Model Context Protocol) tool system that can handle 100+ services with a single source of truth configuration.

### **Current Problem**

- Individual MCP tools don't scale (would need 100+ separate tool files)
- Tool registration is broken (directly accessing private `_tools` dict)
- No single source of truth for service configuration
- Frontend and backend have duplicate service lists

### **Solution**

- Use existing AgentPress XML tool system
- Generate XML-decorated tools dynamically from central config
- Single source of truth in `composio_integrations.py`
- Proper integration with ThreadManager

---

## 🎯 **Implementation Phases**

### **Phase 1: Enhanced Central Configuration**

#### **File: `backend/config/composio_integrations.py`**

**What to do:** Add new fields to the `ComposioIntegration` model to support dynamic tool generation.

**Changes needed:**

1. **Update the ComposioIntegration class:**

```python
class ComposioIntegration(BaseModel):
    """Configuration for a Composio integration."""

    # Existing fields (keep these)
    service_name: str
    integration_id: str
    display_name: str
    description: str
    category: str
    icon_url: Optional[str] = None
    auth_type: str = "oauth2"
    scopes: List[str] = []
    mcp_app_names: List[str] = []

    # NEW FIELDS - Add these
    xml_tag_name: str  # e.g., "gmail-mcp", "notion-mcp"
    enabled: bool = True
    tool_description: str  # For system prompt generation
    requires_connection: bool = True
```

2. **Update existing integrations in COMPOSIO_INTEGRATIONS dict:**

```python
COMPOSIO_INTEGRATIONS: Dict[str, ComposioIntegration] = {
    "gmail": ComposioIntegration(
        service_name="gmail",
        integration_id="c806ea76-3258-4c41-a9a9-784a77fad00d",
        display_name="Gmail",
        description="Connect to Gmail to read, send, and manage emails",
        category="email",
        icon_url="https://example.com/gmail-icon.png",
        auth_type="oauth2",
        scopes=["https://www.googleapis.com/auth/gmail.modify"],
        mcp_app_names=["gmail"],
        # NEW FIELDS
        xml_tag_name="gmail-mcp",
        enabled=True,
        tool_description="Access Gmail through MCP with user authentication for sending, reading, and managing emails",
        requires_connection=True,
    ),
    "notion": ComposioIntegration(
        service_name="notion",
        integration_id="92ae7e87-2f68-4401-b63e-403115a4af59",
        display_name="Notion",
        description="Connect to Notion to manage databases and pages",
        category="productivity",
        icon_url="https://cdn.jsdelivr.net/gh/ComposioHQ/open-logos@master/notion.svg",
        auth_type="oauth2",
        scopes=["read", "write"],
        mcp_app_names=["notion"],
        # NEW FIELDS
        xml_tag_name="notion-mcp",
        enabled=True,
        tool_description="Access Notion through MCP with user authentication for managing pages, databases, and content",
        requires_connection=True,
    ),
}
```

**⚠️ Important Notes:**

- Keep ALL existing fields unchanged
- Only ADD the new fields
- The `xml_tag_name` should follow the pattern: `{service_name}-mcp`
- Set `enabled=True` for services that should be available
- The `tool_description` will be used in the system prompt

---

### **Phase 2: Dynamic XML Tool Generator**

#### **File: `backend/agent/tools/mcp_xml_tools.py` (NEW FILE)**

**What to do:** Create a new file that generates XML-decorated tool functions dynamically.

**Create this entire file:**

```python
"""
Dynamic MCP XML Tool Generator

This module generates XML-decorated tool functions from composio_integrations.py.
Each function uses the AgentPress @xml_schema decorator and calls ComposioMCPTool.
"""

from typing import List, Callable, Dict, Any, Optional
from agentpress.tool import xml_schema
from .composio_mcp_tool import ComposioMCPTool
from config.composio_integrations import get_all_integrations, ComposioIntegration
from services.composio_auth_service import ComposioAuthService
from utils.logger import logger


def create_mcp_xml_tool(integration: ComposioIntegration, auth_service: ComposioAuthService) -> Callable:
    """
    Create an XML-decorated tool function for a specific service.

    Args:
        integration: Service configuration from composio_integrations.py
        auth_service: ComposioAuthService instance for authentication

    Returns:
        XML-decorated function that can be registered with AgentPress
    """

    @xml_schema(
        tag_name=integration.xml_tag_name,
        mappings=[{
            "param_name": "command",
            "node_type": "content",
            "path": ".",
            "required": True
        }],
        example=f"<{integration.xml_tag_name}>Send an <NAME_EMAIL></{integration.xml_tag_name}>"
    )
    async def mcp_tool_function(command: str, **kwargs) -> str:
        """
        Dynamically generated MCP tool function.

        Args:
            command: Natural language command for the service
            **kwargs: Additional context (includes user_id)

        Returns:
            String result from the MCP tool execution
        """
        # Extract user_id from context (passed by AgentPress)
        user_id = kwargs.get('user_id')
        if not user_id:
            return f"❌ User authentication required for {integration.display_name}"

        logger.info(f"Executing {integration.service_name} MCP tool for user {user_id}: {command[:50]}...")

        # Create ComposioMCPTool instance
        tool = ComposioMCPTool(
            user_id=user_id,
            auth_service=auth_service,
            service_name=integration.service_name,
            name=integration.display_name,
            description=integration.description
        )

        # Initialize the tool (checks for user connection)
        if not await tool.initialize():
            return f"❌ {integration.display_name} not connected for this user. Please connect your {integration.display_name} account first."

        try:
            # Execute the command using ComposioMCPTool
            result_generator = tool.execute(action=command)
            result_parts = []

            async for part in result_generator:
                result_parts.append(part)

            final_result = "".join(result_parts)
            logger.info(f"Successfully executed {integration.service_name} MCP tool for user {user_id}")
            return final_result

        except Exception as e:
            error_msg = f"❌ Error executing {integration.display_name} command: {str(e)}"
            logger.error(f"MCP tool execution failed for {integration.service_name}: {e}")
            return error_msg

    # Set function name dynamically for AgentPress registration
    mcp_tool_function.__name__ = f"{integration.service_name}_mcp_tool"
    mcp_tool_function.__doc__ = integration.tool_description

    return mcp_tool_function


def generate_mcp_xml_tools(auth_service: ComposioAuthService) -> List[Callable]:
    """
    Generate XML tool functions for all enabled integrations.

    Args:
        auth_service: ComposioAuthService instance

    Returns:
        List of XML-decorated tool functions ready for registration
    """
    tools = []

    for integration in get_all_integrations():
        if integration.enabled:
            try:
                tool_func = create_mcp_xml_tool(integration, auth_service)
                tools.append(tool_func)
                logger.debug(f"Generated XML tool for {integration.service_name}")
            except Exception as e:
                logger.error(f"Failed to generate XML tool for {integration.service_name}: {e}")

    logger.info(f"Generated {len(tools)} MCP XML tools")
    return tools


def get_mcp_tool_descriptions() -> Dict[str, str]:
    """
    Get tool descriptions for system prompt generation.

    Returns:
        Dict mapping XML tag names to tool descriptions
    """
    descriptions = {}

    for integration in get_all_integrations():
        if integration.enabled:
            descriptions[integration.xml_tag_name] = {
                'service_name': integration.service_name,
                'display_name': integration.display_name,
                'description': integration.tool_description,
                'xml_tag': integration.xml_tag_name
            }

    return descriptions
```

**⚠️ Important Notes:**

- This file must be created in the `backend/agent/tools/` directory
- The `@xml_schema` decorator is from AgentPress - don't modify it
- Each generated function calls the existing `ComposioMCPTool`
- Error handling is crucial - always return user-friendly error messages

---

### **Phase 3: Fix Tool Registration in agent/run.py**

#### **File: `backend/agent/run.py`**

**What to do:** Replace the broken MCP tool registration code with proper AgentPress integration.

**Find this section (around lines 140-150):**

```python
# OLD BROKEN CODE - REMOVE THIS ENTIRE SECTION
for tool in user_tools:
    # Register each user-specific tool
    tool_name = f"{tool.service_name}_mcp_tool"
    thread_manager.tool_registry._tools[tool_name] = {
        "instance": tool,
        "schema": None,  # Will be populated when tool schemas are requested
    }
    logger.debug(f"Registered dynamic tool: {tool_name}")
```

**Replace with this NEW CODE:**

```python
# NEW DYNAMIC XML TOOL SYSTEM
from agent.tools.mcp_xml_tools import generate_mcp_xml_tools

if user_id:
    try:
        # Initialize ComposioAuthService with environment variables
        auth_service = ComposioAuthService(
            composio_api_key=os.getenv("COMPOSIO_API_KEY"),
            supabase_url=os.getenv("SUPABASE_URL"),
            supabase_key=os.getenv("SUPABASE_SERVICE_ROLE_KEY"),
        )

        # Generate XML tools dynamically from composio_integrations.py
        mcp_xml_tools = generate_mcp_xml_tools(auth_service)

        # Register each tool properly with AgentPress ThreadManager
        for tool_func in mcp_xml_tools:
            # Use proper AgentPress registration method
            thread_manager.add_tool(tool_func, user_id=user_id)
            logger.debug(f"Registered XML tool: {tool_func.__name__}")

        logger.info(f"Loaded {len(mcp_xml_tools)} dynamic MCP XML tools for user {user_id}")

    except Exception as e:
        logger.error(f"Failed to load dynamic MCP XML tools for user {user_id}: {e}")
        logger.warning("MCP tools will not be available for this session")
else:
    logger.info("No user_id provided, skipping MCP tool loading")
```

**⚠️ Important Notes:**

- Remove the OLD code completely - don't leave it commented out
- The import statement goes at the top of the file with other imports
- Make sure the indentation matches the surrounding code
- This code should be in the MCP Tools section of the function

---

### **Phase 4: Add Dynamic Services API Endpoint**

#### **File: `backend/routers/mcp_connections.py`**

**What to do:** Update the existing `/services/available` endpoint to use dynamic configuration.

**Find this existing endpoint:**

```python
@router.get("/services/available", response_model=ServiceListResponse)
async def get_available_services():
```

**Replace the entire function body with:**

```python
@router.get("/services/available", response_model=ServiceListResponse)
async def get_available_services():
    """
    Get all available services for connection.
    Returns dynamic list from composio_integrations.py (single source of truth).
    """
    try:
        logger.info("📋 Getting available services from composio_integrations.py")

        # Import here to get latest config
        from config.composio_integrations import get_all_integrations

        services = []
        for integration in get_all_integrations():
            if integration.enabled:
                services.append(ServiceIntegration(
                    service_name=integration.service_name,
                    display_name=integration.display_name,
                    description=integration.description,
                    category=integration.category,
                    icon_url=integration.icon_url,
                    auth_type=integration.auth_type,
                    available=True  # All enabled integrations are available
                ))

        logger.info(f"✅ Found {len(services)} available services from config")
        return ServiceListResponse(services=services)

    except Exception as e:
        logger.error(f"Error getting available services from config: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get available services: {str(e)}"
        )
```

**⚠️ Important Notes:**

- Only replace the function body, keep the decorator and function signature
- The `ServiceIntegration` and `ServiceListResponse` models should already exist
- This endpoint now reads from `composio_integrations.py` instead of hardcoded data

---

### **Phase 5: Dynamic System Prompt Generation**

#### **File: `backend/agent/prompt.py`**

**What to do:** Replace hardcoded MCP tool descriptions with dynamic generation.

**Step 1: Add import at the top of the file:**

```python
# Add this import with the other imports
from typing import Dict, Any
```

**Step 2: Add this function before the SYSTEM_PROMPT definition:**

````python
def get_mcp_tools_prompt_section() -> str:
    """
    Generate MCP tools section dynamically from composio_integrations.py.
    This ensures the system prompt always matches available services.
    """
    try:
        from agent.tools.mcp_xml_tools import get_mcp_tool_descriptions

        tool_descriptions = get_mcp_tool_descriptions()

        if not tool_descriptions:
            return ""

        prompt_section = "\n## DYNAMIC MCP TOOLS\n\n"
        prompt_section += "You have access to the following MCP (Model Context Protocol) tools based on user connections:\n\n"

        for xml_tag, info in tool_descriptions.items():
            service_name = info['display_name']
            prompt_section += f"### {service_name.upper()} MCP TOOL\n\n"
            prompt_section += f"The {service_name} MCP Tool allows you to interact with {service_name} using natural language instructions.\n\n"
            prompt_section += f"**When to use:** {info['description']}\n\n"
            prompt_section += f"**How to call:**\n"
            prompt_section += f"```xml\n<{xml_tag}>\n<your natural language {service_name} command here>\n</{xml_tag}>\n```\n\n"
            prompt_section += f"**Example:**\n"
            prompt_section += f"```xml\n<{xml_tag}>Send an <NAME_EMAIL> with subject 'Hello'</{xml_tag}>\n```\n\n"

        return prompt_section

    except Exception as e:
        # Fallback to empty string if dynamic generation fails
        logger.error(f"Failed to generate dynamic MCP tools prompt section: {e}")
        return ""
````

**Step 3: Find the hardcoded MCP tool sections in SYSTEM_PROMPT and replace them:**

**Remove these hardcoded sections:**

- `## 3.1.1 GMAIL MCP TOOL`
- `## 3.1.2 NOTION MCP TOOL`
- `## 3.1.3 GOOGLE DRIVE MCP TOOL`
- `## 3.1.4 SLACK MCP TOOL`
- `## 3.1.5 APOLLO MCP TOOL`

**Replace with dynamic generation in the SYSTEM_PROMPT:**

Find this line in the SYSTEM_PROMPT:

```python
SYSTEM_PROMPT = f"""
You are Atlas, an autonomous AI Agent

# 1. CORE IDENTITY & CAPABILITIES
```

**Add the dynamic MCP section after the core capabilities:**

```python
SYSTEM_PROMPT = f"""
You are Atlas, an autonomous AI Agent

# 1. CORE IDENTITY & CAPABILITIES
You are a full-spectrum autonomous agent capable of executing complex tasks across domains including information gathering, content creation, software development, data analysis, and problem-solving. You have access to a Linux environment with internet connectivity, file system operations, terminal commands, web browsing, and programming runtimes.

{get_mcp_tools_prompt_section()}

# 2. EXECUTION ENVIRONMENT
```

**⚠️ Important Notes:**

- Remove ALL hardcoded MCP tool sections completely
- The dynamic section will automatically include all enabled services
- If dynamic generation fails, it returns empty string (graceful fallback)
- The system prompt will now always match the available services

---

### **Phase 6: Frontend Integration**

#### **File: `frontend/hooks/useServices.ts` (NEW FILE)**

**What to do:** Create a React hook to fetch services dynamically from the backend.

**Create this new file in the `frontend/hooks/` directory:**

```typescript
import { useQuery } from "@tanstack/react-query";

export interface ServiceIntegration {
  service_name: string;
  display_name: string;
  description: string;
  category: string;
  icon_url?: string;
  auth_type: string;
  available: boolean;
}

export function useAvailableServices() {
  return useQuery({
    queryKey: ["services", "available"],
    queryFn: async (): Promise<ServiceIntegration[]> => {
      const response = await fetch("/api/v1/connections/services/available");
      if (!response.ok) {
        throw new Error("Failed to fetch available services");
      }
      const data = await response.json();
      return data.services;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
}
```

#### **File: `frontend/workers/suite/mcp/page.tsx`**

**What to do:** Update the MCP connections page to use dynamic services.

**Step 1: Add the import at the top:**

```typescript
import { useAvailableServices } from "@/hooks/useServices";
```

**Step 2: Replace hardcoded services with dynamic loading:**

**Find this section (around line 20-40):**

```typescript
// OLD HARDCODED SERVICES - REMOVE THIS
const services = [
  {
    name: "gmail",
    displayName: "Gmail",
    description: "Connect to Gmail to read, send, and manage emails",
    // ... other hardcoded services
  },
];
```

**Replace with:**

```typescript
// NEW DYNAMIC SERVICES
const {
  data: availableServices,
  isLoading: servicesLoading,
  error: servicesError,
} = useAvailableServices();
```

**Step 3: Update the rendering logic:**

**Find the services mapping section:**

```typescript
{
  services.map((service) => (
    <ServiceConnectionCard
      key={service.name}
      // ... props
    />
  ));
}
```

**Replace with:**

```typescript
{
  servicesLoading ? (
    <div className="text-center py-8">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
      <p className="mt-2 text-gray-600">Loading available services...</p>
    </div>
  ) : servicesError ? (
    <div className="text-center py-8 text-red-600">
      <p>Failed to load services. Please refresh the page.</p>
    </div>
  ) : (
    availableServices?.map((service) => (
      <ServiceConnectionCard
        key={service.service_name}
        service={{
          name: service.service_name,
          displayName: service.display_name,
          description: service.description,
          category: service.category,
          iconUrl: service.icon_url,
        }}
        connectionStatus={connectionStatuses[service.service_name]}
        onConnect={() => handleConnect(service.service_name)}
        onDisconnect={() => handleDisconnect(service.service_name)}
      />
    ))
  );
}
```

**⚠️ Important Notes:**

- Make sure React Query is properly set up in your app
- The loading and error states provide better UX
- Update the `ServiceConnectionCard` props to match the new data structure
- Test the page loads correctly with dynamic data

---

## 🧪 **Testing Instructions**

### **Phase 1 Testing:**

1. Start the backend server
2. Check that `composio_integrations.py` imports without errors
3. Verify new fields are accessible: `get_integration('gmail').xml_tag_name`

### **Phase 2 Testing:**

1. Import the new module: `from agent.tools.mcp_xml_tools import generate_mcp_xml_tools`
2. Test tool generation: `tools = generate_mcp_xml_tools(auth_service)`
3. Verify tools have proper XML schemas

### **Phase 3 Testing:**

1. Start an agent session with a user_id
2. Check logs for "Loaded X dynamic MCP XML tools"
3. Verify no errors about `_tools` dict access

### **Phase 4 Testing:**

1. Call `GET /api/v1/connections/services/available`
2. Verify response includes all enabled services from config
3. Check that adding a new service to config appears in API

### **Phase 5 Testing:**

1. Check system prompt includes dynamic MCP tools section
2. Verify prompt updates when services are added/removed
3. Test graceful fallback if dynamic generation fails

### **Phase 6 Testing:**

1. Load the MCP connections page
2. Verify services load dynamically from API
3. Test connection/disconnection flows still work

---

## ⚠️ **Common Pitfalls to Avoid**

1. **Don't modify AgentPress core files** - only use the public APIs
2. **Don't access private `_tools` dict** - use `thread_manager.add_tool()`
3. **Don't hardcode service lists** - always read from `composio_integrations.py`
4. **Don't forget error handling** - always wrap in try/catch blocks
5. **Don't break existing functionality** - test OAuth flows still work
6. **Don't skip imports** - make sure all new imports are added
7. **Don't forget logging** - add appropriate log statements for debugging

---

## 🚀 **Adding New Services (After Implementation)**

Once this system is implemented, adding a new service is simple:

1. **Add to `composio_integrations.py`:**

```python
"newservice": ComposioIntegration(
    service_name="newservice",
    integration_id="your-integration-id",
    display_name="New Service",
    description="Description of the service",
    category="productivity",
    xml_tag_name="newservice-mcp",
    enabled=True,
    tool_description="Access New Service through MCP",
    # ... other fields
),
```

2. **That's it!** The service will automatically:
   - Appear in the frontend services list
   - Be available as `<newservice-mcp>` XML tool
   - Be included in the system prompt
   - Work with existing OAuth flows

---

## 📞 **Support**

If you encounter issues during implementation:

1. **Check the logs** - all components have detailed logging
2. **Verify imports** - make sure all new files are importable
3. **Test incrementally** - implement one phase at a time
4. **Check existing functionality** - ensure OAuth flows still work
5. **Ask for help** - provide specific error messages and logs

**Good luck with the implementation! 🎉**
