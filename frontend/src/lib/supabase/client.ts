import { createBrowserClient } from '@supabase/ssr';

export const createClient = () => {
  // Use environment variables for Supabase URL and key
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('Database configuration error - check environment variables');
  }

  return createBrowserClient(supabaseUrl, supabaseAnonKey);
};
