/**
 * Utility to determine the correct backend API URL at runtime
 * 
 * This handles the case where the frontend is running in a browser
 * and needs to connect to the backend, but the NEXT_PUBLIC_BACKEND_URL
 * is set to "http://backend:8000/api" for Docker networking.
 */

// Get the configured backend URL from environment variables
const configuredApiUrl = process.env.NEXT_PUBLIC_BACKEND_URL || '';

/**
 * Returns the appropriate backend API URL based on the runtime environment
 * 
 * If the configured URL contains "backend:" (Docker hostname), it will be
 * replaced with "localhost:" when running in a browser.
 */
export function getApiUrl(): string {
  // If we're in a browser and the URL contains "backend:"
  if (typeof window !== 'undefined' && configuredApiUrl.includes('backend:')) {
    // Replace "backend:" with "localhost:" for browser access
    return configuredApiUrl.replace('backend:', 'localhost:');
  }
  
  // Otherwise, use the configured URL as-is
  return configuredApiUrl;
}

// Export a singleton instance for use throughout the app
export const API_URL = getApiUrl();
