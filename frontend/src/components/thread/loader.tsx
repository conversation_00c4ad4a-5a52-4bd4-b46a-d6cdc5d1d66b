'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface AgentLoaderProps {
  agentStatus: 'idle' | 'running' | 'connecting' | 'error';
  avatar?: React.ReactNode;
}

const loadingMessages = [
    'Waking Atlas...',
    'Linking systems...',
    'Priming directives...',
    'Running silent ops...',
    'Decrypting layers...',
    'Aligning cognition...',
    'Parsing intent...',
    'Spooling routines...',
    'Mapping the unknown...',
    'Engaging substructure...',
    'Calibrating vectors...',
    'Initializing neural webs...',
    'Activating protocols...',
    'Scanning data streams...',
    'Bootstrapping core logic...',
    'Synchronizing frequencies...',
    'Loading knowledge base...',
    'Establishing connections...',
    'Compiling thoughts...',
    'Channeling consciousness...',
  ];

export function AgentLoader({ agentStatus, avatar }: AgentLoaderProps) {
  const [index, setIndex] = useState(0);

  useEffect(() => {
    const id = setInterval(() => {
      setIndex((state) => {
        if (state >= loadingMessages.length - 1) return 0;
        return state + 1;
      });
    }, 2500); // Increased from 1500ms to 2500ms for slower transitions
    return () => clearInterval(id);
  }, []);

  if (agentStatus === 'idle') return null;

  // Determine if we should center the avatar (when actively loading) or top-align (when showing message/error)
  const isActivelyLoading = agentStatus === 'running' || agentStatus === 'connecting';
  const alignmentClass = isActivelyLoading ? 'items-center' : 'items-start';

  if (avatar) {
    // When avatar is provided, render it alongside the loader with proper alignment
    return (
      <div className={`flex gap-3 w-full ${alignmentClass}`}>
        <div className="flex-shrink-0">
          {avatar}
        </div>
        <div className="flex-1 space-y-2 w-full h-16 bg-background">
          <div className="max-w-[90%] animate-shimmer bg-transparent h-full p-0.5 text-sm border rounded-xl shadow-sm relative overflow-hidden">
            <div className="rounded-md bg-background flex px-5 items-center justify-start h-full relative z-10">
              <div className="flex flex-col items-start w-full">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={loadingMessages[index]}
                    initial={{ y: 20, opacity: 0, filter: "blur(8px)" }}
                    animate={{ y: 0, opacity: 1, filter: "blur(0px)" }}
                    exit={{ y: -20, opacity: 0, filter: "blur(8px)" }}
                    transition={{ duration: 0.4, ease: "easeInOut" }}
                    className="whitespace-nowrap text-muted-foreground"
                  >
                    {loadingMessages[index]}
                  </motion.div>
                </AnimatePresence>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Original loader without avatar (fallback for existing usage)
  return (
    <div className="flex-1 space-y-2 w-full h-16 bg-background">
      <div className="max-w-[90%] animate-shimmer bg-transparent h-full p-0.5 text-sm border rounded-xl shadow-sm relative overflow-hidden">
        <div className="rounded-md bg-background flex px-5 items-center justify-start h-full relative z-10">
          <div className="flex flex-col items-start w-full">
            <AnimatePresence mode="wait">
              <motion.div
                key={loadingMessages[index]}
                initial={{ y: 20, opacity: 0, filter: "blur(8px)" }}
                animate={{ y: 0, opacity: 1, filter: "blur(0px)" }}
                exit={{ y: -20, opacity: 0, filter: "blur(8px)" }}
                transition={{ duration: 0.4, ease: "easeInOut" }}
                className="whitespace-nowrap text-muted-foreground"
              >
                {loadingMessages[index]}
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>
    </div>
  );
}
