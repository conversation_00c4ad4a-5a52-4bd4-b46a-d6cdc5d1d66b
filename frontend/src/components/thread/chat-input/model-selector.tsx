'use client';

import React, { useState, useRef, useEffect } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Check, ChevronDown, Lock, Brain, Zap, Circle } from 'lucide-react';
import { ModelOption, SubscriptionStatus } from './_use-model-selection';
import { PaywallDialog } from '@/components/payment/paywall-dialog';
import { cn } from '@/lib/utils';

interface ModelSelectorProps {
  selectedModel: string;
  onModelChange: (modelId: string) => void;
  modelOptions: ModelOption[];
  canAccessModel: (modelId: string) => boolean;
}

export const ModelSelector: React.FC<ModelSelectorProps> = ({
  selectedModel,
  onModelChange,
  modelOptions,
  canAccessModel,
}) => {
  const [paywallOpen, setPaywallOpen] = useState(false);
  const [lockedModel, setLockedModel] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSelect = (id: string) => {
    if (canAccessModel(id)) {
      onModelChange(id);
      setIsOpen(false);
    } else {
      setLockedModel(id);
      setPaywallOpen(true);
    }
  };

  const closeDialog = () => {
    setPaywallOpen(false);
    setLockedModel(null);
  };

  const selectedModelData = modelOptions.find((o) => o.id === selectedModel);
  const selectedLabel = selectedModelData?.label || 'Model';

  return (
    <div className="relative" ref={dropdownRef}>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <div className={cn(
            "flex items-center gap-1.5 text-sm px-2 h-8 cursor-pointer text-foreground",
            "hover:text-foreground/80 transition-colors"
          )}>
            <span className={cn(
              selectedModel ? "font-medium" : "text-muted-foreground"
            )}>
              {selectedLabel}
            </span>
            <ChevronDown className={cn(
              "h-3.5 w-3.5 opacity-70 transition-transform",
              isOpen ? "transform rotate-180" : ""
            )} />
          </div>
        </DropdownMenuTrigger>

        <DropdownMenuContent
          align="start"
          sideOffset={4}
          className="w-40 p-1 bg-popover rounded-md shadow-lg border border-border/50"
          onInteractOutside={(e) => {
            const isClickInside = dropdownRef.current?.contains(e.target as Node);
            if (isClickInside) e.preventDefault();
          }}
        >
          <div className="space-y-0.5">
            {modelOptions.map((opt) => {
              const accessible = canAccessModel(opt.id);
              const isSelected = selectedModel === opt.id;

              return (
                <DropdownMenuItem
                  key={opt.id}
                  className={cn(
                    "flex items-center gap-2 px-2 py-1.5 text-sm rounded-[4px] cursor-pointer",
                    "hover:bg-accent/50 focus:bg-accent/50",
                    !accessible ? "opacity-50 cursor-not-allowed" : ""
                  )}
                  onSelect={(e) => {
                    e.preventDefault();
                    if (accessible) handleSelect(opt.id);
                  }}
                  onClick={(e) => {
                    if (!accessible) {
                      e.preventDefault();
                      setLockedModel(opt.id);
                      setPaywallOpen(true);
                    }
                  }}
                >
                  <div className={cn(
                    "flex items-center justify-center h-4 w-4 rounded-sm border shrink-0",
                    isSelected
                      ? "bg-blue-500 border-blue-500"
                      : "border-border"
                  )}>
                    {isSelected && <Check className="h-3 w-3 text-white" />}
                  </div>
                  <div className="flex items-center gap-1.5">
                    <span className="text-sm">{opt.label}</span>
                    {!accessible && <Lock className="h-3 w-3 text-muted-foreground" />}
                    {opt.id === 'gemini-2.0-flash-lite' && (
                      <Zap className="h-3 w-3 text-muted-foreground" />
                    )}
                    {opt.id === 'gemini-2.5-flash' && (
                      <Circle className="h-3 w-3 text-muted-foreground" />
                    )}
                    {opt.id === 'claude-4-sonnet-thinking' && (
                      <Brain className="h-3 w-3 text-muted-foreground" />
                    )}
                  </div>
                </DropdownMenuItem>
              );
            })}
          </div>
        </DropdownMenuContent>
      </DropdownMenu>

      {paywallOpen && (
        <PaywallDialog
          open={true}
          onDialogClose={closeDialog}
          title="Premium Model"
          description={
            lockedModel
              ? `Subscribe to access ${modelOptions.find(
                  (m) => m.id === lockedModel
                )?.label}`
              : 'Subscribe to access premium models'
          }
          ctaText="Subscribe Now"
          cancelText="Maybe Later"
        />
      )}
    </div>
  );
};
