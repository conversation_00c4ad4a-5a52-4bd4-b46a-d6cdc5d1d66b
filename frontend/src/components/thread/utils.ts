import type { ElementType } from 'react';
import React from 'react';
import { useTheme } from 'next-themes';
import { useState, useEffect } from 'react';
import { getToolIcon } from '@/lib/tool-icons';

// Flag to control whether tool result messages are rendered
export const SHOULD_RENDER_TOOL_RESULTS = false;

// Helper function to safely parse JSON strings from content/metadata
export function safeJsonParse<T>(
  jsonString: string | undefined | null,
  fallback: T,
): T {
  if (!jsonString) {
    return fallback;
  }
  try {
    return JSON.parse(jsonString);
  } catch (e) {
    // console.warn('Failed to parse JSON string:', jsonString, e); // Optional: log errors
    return fallback;
  }
}

// Helper function to extract a primary parameter from XML/arguments
export const extractPrimaryParam = (
  toolName: string,
  content: string | undefined,
): string | undefined => {
  if (!content) return undefined;

  // Convert to lowercase for case-insensitive matching
  const normalizedName = toolName.toLowerCase();

  // Extract based on tool type
  switch (normalizedName) {
    case 'execute-command':
      const commandMatch = content.match(/<execute-command>(.*?)<\/execute-command>/s);
      return commandMatch ? commandMatch[1].trim() : undefined;

    case 'web-search':
      const searchMatch = content.match(/search_term="([^"]+)"/);
      return searchMatch ? searchMatch[1] : undefined;

    case 'crawl-webpage':
    case 'scrape-webpage':
      const urlMatch = content.match(/url="([^"]+)"/);
      return urlMatch ? urlMatch[1] : undefined;

    case 'browser-navigate':
      const navigateMatch = content.match(/url="([^"]+)"/);
      return navigateMatch ? navigateMatch[1] : undefined;

    case 'browser-click':
      const clickMatch = content.match(/selector="([^"]+)"/);
      return clickMatch ? clickMatch[1] : undefined;

    case 'browser-extract':
      const extractMatch = content.match(/selector="([^"]+)"/);
      return extractMatch ? extractMatch[1] : undefined;

    case 'browser-fill':
      const fillMatch = content.match(/selector="([^"]+)"/);
      return fillMatch ? fillMatch[1] : undefined;

    case 'browser-wait':
      const waitMatch = content.match(/selector="([^"]+)"/);
      return waitMatch ? waitMatch[1] : undefined;

    default:
      return undefined;
  }
};

export { getToolIcon };
