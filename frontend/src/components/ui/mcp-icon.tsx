import React from 'react';
import { useTheme } from 'next-themes';
import { cn } from '@/lib/utils';

interface McpIconProps {
  size?: number;
  className?: string;
  containerClassName?: string;
}

export const McpIcon: React.FC<McpIconProps> = ({
  size = 24,
  className,
  containerClassName
}) => {
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  const src = resolvedTheme === 'dark'
    ? 'https://unpkg.com/@lobehub/icons-static-png@latest/dark/mcp.png'
    : 'https://unpkg.com/@lobehub/icons-static-png@latest/light/mcp.png';

  const icon = (
    <img
      src={src}
      alt="MCP Icon"
      width={size}
      height={size}
      className={cn('inline-block align-middle', className)}
    />
  );

  if (!containerClassName) {
    return icon;
  }

  return (
    <div className={cn('rounded-lg border bg-background p-2', containerClassName)}>
      {icon}
    </div>
  );
};
