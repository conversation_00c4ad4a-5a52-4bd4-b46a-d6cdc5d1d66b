'use client';

import React from "react";
import { cn } from "@/lib/utils";

interface MainLayoutProps {
  headerContent?: React.ReactNode;
  footerContent?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}

export function MainLayout({
  headerContent,
  footerContent,
  children,
  className,
}: MainLayoutProps) {
  return (
    <div className={cn("flex flex-col min-h-screen", className)}>
      {headerContent && (
        <header className="bg-background w-full">
          {headerContent}
        </header>
      )}

      <main className="flex-1">
        {children}
      </main>

      {footerContent && (
        <footer>
          <div className="w-full max-w-screen py-6">
            {footerContent}
          </div>
        </footer>
      )}
    </div>
  );
}
