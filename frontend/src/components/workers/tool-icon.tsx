import React from 'react';
import { IconType } from 'react-icons';
import { LucideIcon } from 'lucide-react';
import { getToolIcon } from '@/lib/tool-icons';
import { cn } from '@/lib/utils';
import { McpIcon } from '@/components/ui/mcp-icon';
import { FC } from 'react';

interface ToolIconProps {
  toolName: string;
  className?: string;
  size?: number;
  containerClassName?: string;
}

type IconComponent = IconType | LucideIcon | FC<any>;

export const ToolIcon: React.FC<ToolIconProps> = ({
  toolName,
  className,
  size = 24,
  containerClassName,
}) => {
  // Check if it's an MCP tool (either starts with 'mcp' or ends with '-mcp')
  const normalizedName = toolName.toLowerCase();
  const isMcpTool = normalizedName.startsWith('mcp') || normalizedName.endsWith('-mcp');

  if (isMcpTool) {
    return (
      <McpIcon
        size={size}
        className={className}
        containerClassName={containerClassName}
      />
    );
  }

  const Icon = getToolIcon(toolName) as IconComponent;

  if (!containerClassName) {
    return <Icon size={size} className={className} />;
  }

  return (
    <div className={cn('rounded-lg border bg-background p-2', containerClassName)}>
      <Icon size={size} className={cn('text-foreground/70', className)} />
    </div>
  );
};
