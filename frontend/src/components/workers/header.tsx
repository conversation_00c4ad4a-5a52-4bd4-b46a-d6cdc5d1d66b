import React from 'react';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { cn } from '@/lib/utils';

interface WorkerHeaderProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  action?: React.ReactNode;
  showBackButton?: boolean;
  className?: string;
}

export function WorkerHeader({
  title,
  description,
  icon,
  action,
  showBackButton = false,
  className,
}: WorkerHeaderProps) {
  return (
    <div className={cn("border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60", className)}>
      <div className="px-4 md:px-6 py-8">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-4">
            {showBackButton && (
              <Button
                variant="outline"
                size="icon"
                asChild
                className="shrink-0"
              >
                <Link href="/workers">
                  <ArrowLeft className="h-4 w-4" />
                </Link>
              </Button>
            )}
            <div className="flex items-center gap-3">
              {icon && (
                <div className="rounded-lg border bg-background p-2">
                  {icon}
                </div>
              )}
              <h1 className="text-4xl font-bold tracking-tight">{title}</h1>
            </div>
          </div>
          {action && (
            <div className="flex items-center gap-2">
              {action}
            </div>
          )}
        </div>
        <p className="text-lg text-muted-foreground">
          {description}
        </p>
      </div>
    </div>
  );
}
