'use client';

import { useState, useEffect } from 'react';
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { FileText, Save, X } from 'lucide-react';

interface Workflow {
  workflow_id: string;
  name: string;
  description: string;
  status: string;
  execution_count: number;
  source_thread_id?: string;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
}

interface EditWorkflowModalProps {
  workflow: Workflow | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (workflow: Partial<Workflow>) => Promise<void>;
}

export function EditWorkflowModal({ workflow, open, onOpenChange, onSave }: EditWorkflowModalProps) {
  const [formData, setFormData] = useState<Partial<Workflow>>({});
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (workflow) {
      setFormData({
        name: workflow.name,
        description: workflow.description,
        status: workflow.status,
      });
    }
  }, [workflow]);

  const handleSave = async () => {
    if (!workflow || !formData.name?.trim()) return;

    try {
      setSaving(true);
      await onSave(formData);
      onOpenChange(false);
    } catch (error) {
      console.error('Error saving workflow:', error);
    } finally {
      setSaving(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950/50 dark:text-green-400 dark:border-green-800';
      case 'archived': return 'bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-950/50 dark:text-gray-400 dark:border-gray-800';
      case 'template': return 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950/50 dark:text-blue-400 dark:border-blue-800';
      default: return 'bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-950/50 dark:text-gray-400 dark:border-gray-800';
    }
  };

  if (!workflow) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Edit Workflow</span>
          </DialogTitle>
          <DialogDescription>
            Update the workflow details and settings.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Current Status Preview */}
          <div className="p-4 rounded-lg border bg-muted/20">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className={`${getStatusColor(workflow.status)} text-xs font-medium`}>
                  {workflow.status}
                </Badge>
                <span className="text-sm text-muted-foreground">
                  {workflow.execution_count} executions
                </span>
              </div>
            </div>
            <div className="text-sm text-muted-foreground">
              Created: {new Date(workflow.created_at).toLocaleDateString()}
            </div>
          </div>

          {/* Editable Fields */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Workflow Name *</Label>
              <Input
                id="name"
                placeholder="Enter workflow name..."
                value={formData.name || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              />
              <div className="text-xs text-muted-foreground">
                A clear, descriptive name for your workflow.
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Describe what this workflow does..."
                value={formData.description || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
              />
              <div className="text-xs text-muted-foreground">
                Explain the purpose and functionality of this workflow.
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={formData.status || 'active'}
                onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select workflow status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="archived">Archived</SelectItem>
                  <SelectItem value="template">Template</SelectItem>
                </SelectContent>
              </Select>
              <div className="text-xs text-muted-foreground">
                Set the workflow status: active (ready to use), archived (hidden), or template (reusable pattern).
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-2 pt-4 border-t">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={saving}
          >
            <X className="mr-2 h-4 w-4" />
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={saving || !formData.name?.trim()}
          >
            {saving ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
