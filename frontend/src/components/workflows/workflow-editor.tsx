'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  FileText,
  Zap,
  Settings,
  Edit,
  Trash2,
  Plus,
  ChevronUp,
  ChevronDown,
  Network,
  ArrowDown,
  Circle
} from 'lucide-react';

interface WorkflowStep {
  step_id: string;
  step_order: number;
  tool_name: string;
  tool_parameters: Record<string, any>;
  explanation_text?: string;
  user_context?: string;
  parameter_config?: Record<string, any>;
}

interface WorkflowEditorProps {
  steps: WorkflowStep[];
  onUpdateStep: (step: WorkflowStep) => Promise<void>;
  onAddStep: (step: Omit<WorkflowStep, 'step_id'>) => Promise<void>;
  onDeleteStep: (stepId: string) => Promise<void>;
  onEditStep: (step: WorkflowStep) => void;
}

const TOOL_OPTIONS = [
  { value: 'edit_file', label: 'Edit File', icon: FileText },
  { value: 'create_file', label: 'Create File', icon: FileText },
  { value: 'run_terminal_cmd', label: 'Run Terminal Command', icon: Zap },
  { value: 'execute_command', label: 'Execute Command', icon: Zap },
  { value: 'read_file', label: 'Read File', icon: FileText },
  { value: 'codebase_search', label: 'Search Codebase', icon: Settings },
  { value: 'grep_search', label: 'Grep Search', icon: Settings },
];

export function WorkflowEditor({
  steps,
  onUpdateStep,
  onAddStep,
  onDeleteStep,
  onEditStep
}: WorkflowEditorProps) {
  const [addingStep, setAddingStep] = useState(false);
  const [newStep, setNewStep] = useState({
    tool_name: '',
    explanation_text: '',
    user_context: '',
    tool_parameters: '{}',
  });

  const getToolIcon = (toolName: string) => {
    const tool = TOOL_OPTIONS.find(t => t.value === toolName.toLowerCase());
    const IconComponent = tool?.icon || Settings;
    return <IconComponent className="h-4 w-4" />;
  };

  const handleAddStep = async () => {
    try {
      let parsedParameters = {};
      try {
        parsedParameters = JSON.parse(newStep.tool_parameters);
      } catch (e) {
        parsedParameters = {};
      }

      const stepToAdd = {
        step_order: steps.length + 1,
        tool_name: newStep.tool_name,
        tool_parameters: parsedParameters,
        explanation_text: newStep.explanation_text,
        user_context: newStep.user_context,
        parameter_config: {},
      };

      await onAddStep(stepToAdd);

      setNewStep({
        tool_name: '',
        explanation_text: '',
        user_context: '',
        tool_parameters: '{}',
      });
      setAddingStep(false);
    } catch (error) {
      console.error('Error adding step:', error);
    }
  };

  const handleDeleteAndRenumber = async (stepId: string) => {
    await onDeleteStep(stepId);
  };

  const sortedSteps = [...steps].sort((a, b) => a.step_order - b.step_order);

  return (
    <Card className="bg-background border-border/50">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-7">
        <div className="space-y-1.5">
          <CardTitle className="flex items-center space-x-2">
            <Network className="h-5 w-5" />
            <span>Workflow Steps</span>
          </CardTitle>
          <CardDescription>
            Steps are executed in order from top to bottom.
          </CardDescription>
        </div>
        <Dialog open={addingStep} onOpenChange={setAddingStep}>
          <DialogTrigger asChild>
            <Button size="sm" className="h-9">
              <Plus className="mr-2 h-4 w-4" />
              Add Step
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add New Step</DialogTitle>
              <DialogDescription>
                Create a new step for your workflow.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="tool">Tool</Label>
                <Select
                  value={newStep.tool_name}
                  onValueChange={(value) => setNewStep(prev => ({ ...prev, tool_name: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a tool" />
                  </SelectTrigger>
                  <SelectContent>
                    {TOOL_OPTIONS.map(tool => (
                      <SelectItem key={tool.value} value={tool.value}>
                        <div className="flex items-center space-x-2">
                          <tool.icon className="h-4 w-4" />
                          <span>{tool.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="explanation">Explanation</Label>
                <Textarea
                  id="explanation"
                  placeholder="Describe what this step does..."
                  value={newStep.explanation_text}
                  onChange={(e) => setNewStep(prev => ({ ...prev, explanation_text: e.target.value }))}
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="context">User Context</Label>
                <Textarea
                  id="context"
                  placeholder="Additional context or notes..."
                  value={newStep.user_context}
                  onChange={(e) => setNewStep(prev => ({ ...prev, user_context: e.target.value }))}
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="parameters">Tool Parameters (JSON)</Label>
                <Textarea
                  id="parameters"
                  placeholder='{"example": "value"}'
                  value={newStep.tool_parameters}
                  onChange={(e) => setNewStep(prev => ({ ...prev, tool_parameters: e.target.value }))}
                  rows={3}
                  className="font-mono text-sm"
                />
              </div>
            </div>
            <div className="flex justify-end space-x-2 pt-4">
              <Button variant="outline" onClick={() => setAddingStep(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddStep} disabled={!newStep.tool_name}>
                Add Step
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent className="p-0">
        <div className="flex flex-col min-h-full">
          {/* Full-width Step List with Arrows */}
          <div className="flex-1 overflow-y-auto">
            <div className="p-6 pt-4">
              <div className="space-y-0">
                {sortedSteps.map((step, index) => (
                  <div key={step.step_id} className="relative">
                    <div
                      className="flex items-center justify-between p-4 rounded-xl border bg-blue-50/50 dark:bg-blue-950/20 border-blue-100 dark:border-blue-900/40 transition-colors mb-0"
                    >
                      {/* Step Number */}
                      <div className="flex items-center">
                        <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-500/90 text-white dark:bg-blue-600/80 dark:text-white font-medium text-base mr-4 shadow-sm">
                          {step.step_order}
                        </div>
                        <span className="inline-block px-3 py-1 rounded-full bg-blue-100/80 text-blue-700 dark:bg-blue-900/40 dark:text-blue-200 font-medium text-sm tracking-tight">
                          {step.tool_name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </span>
                      </div>
                      {/* Actions */}
                      <div className="flex items-center space-x-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 hover:bg-blue-100/80 dark:hover:bg-blue-900/40"
                          onClick={() => onEditStep(step)}
                        >
                          <Edit className="h-4 w-4 text-blue-600 dark:text-blue-300" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 hover:bg-destructive/10 hover:text-destructive"
                          onClick={() => handleDeleteAndRenumber(step.step_id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    {/* Only ArrowDown icon, no tail, between steps */}
                    {index < sortedSteps.length - 1 && (
                      <div className="flex justify-center py-2">
                        <ArrowDown className="h-6 w-6 text-black dark:text-white" />
                      </div>
                    )}
                  </div>
                ))}
                {sortedSteps.length === 0 && (
                  <div className="text-center py-16 text-muted-foreground">
                    <div className="w-14 h-14 mx-auto mb-4 rounded-xl bg-muted/30 flex items-center justify-center">
                      <Settings className="h-7 w-7 text-muted-foreground/50" />
                    </div>
                    <h4 className="font-medium mb-2">No steps yet</h4>
                    <p className="text-sm">Add your first step to get started.</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
