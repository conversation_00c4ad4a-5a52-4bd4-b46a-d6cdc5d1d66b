'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { FileText, Zap, Settings, Save, X } from 'lucide-react';

interface WorkflowStep {
  step_id: string;
  step_order: number;
  tool_name: string;
  tool_parameters: Record<string, any>;
  explanation_text?: string;
  user_context?: string;
  parameter_config?: Record<string, any>;
}

interface EditStepModalProps {
  step: WorkflowStep | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (step: WorkflowStep) => Promise<void>;
}

export function EditStepModal({ step, open, onOpenChange, onSave }: EditStepModalProps) {
  const [formData, setFormData] = useState<Partial<WorkflowStep>>({});
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (step) {
      setFormData({
        step_id: step.step_id,
        step_order: step.step_order,
        tool_name: step.tool_name,
        tool_parameters: step.tool_parameters,
        explanation_text: step.explanation_text || '',
        user_context: step.user_context || '',
        parameter_config: step.parameter_config || {},
      });
    }
  }, [step]);

  const handleSave = async () => {
    if (!step || !formData) return;

    try {
      setSaving(true);
      const updatedStep: WorkflowStep = {
        ...step,
        explanation_text: formData.explanation_text,
        user_context: formData.user_context,
      };

      await onSave(updatedStep);
      onOpenChange(false);
    } catch (error) {
      console.error('Error saving step:', error);
    } finally {
      setSaving(false);
    }
  };

  const getToolIcon = (toolName: string) => {
    switch (toolName?.toLowerCase()) {
      case 'edit_file':
      case 'create_file':
        return <FileText className="h-4 w-4" />;
      case 'run_terminal_cmd':
      case 'execute_command':
        return <Zap className="h-4 w-4" />;
      default:
        return <Settings className="h-4 w-4" />;
    }
  };

  const getToolColor = (toolName: string) => {
    switch (toolName?.toLowerCase()) {
      case 'edit_file':
      case 'create_file':
        return 'bg-blue-50 border-blue-200 text-blue-700 dark:bg-blue-950/50 dark:border-blue-800 dark:text-blue-400';
      case 'run_terminal_cmd':
      case 'execute_command':
        return 'bg-green-50 border-green-200 text-green-700 dark:bg-green-950/50 dark:border-green-800 dark:text-green-400';
      default:
        return 'bg-purple-50 border-purple-200 text-purple-700 dark:bg-purple-950/50 dark:border-purple-800 dark:text-purple-400';
    }
  };

  if (!step) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <div className="flex items-center space-x-2">
              <div className="flex items-center justify-center w-6 h-6 rounded-full bg-primary/10 text-primary text-xs font-bold">
                {step.step_order}
              </div>
              {getToolIcon(step.tool_name)}
            </div>
            <span>Edit Step: {step.tool_name}</span>
          </DialogTitle>
          <DialogDescription>
            Update the details and context for this workflow step.
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 min-h-0 overflow-y-auto space-y-6">
          {/* Step Preview */}
          <div className="p-4 rounded-lg border bg-muted/20">
            <div className="flex items-center space-x-2 mb-2">
              <Badge variant="outline" className={`${getToolColor(step.tool_name)} text-xs font-medium`}>
                Step {step.step_order}
              </Badge>
              <Badge variant="outline" className="text-xs">
                {step.tool_name}
              </Badge>
            </div>
            <div className="text-sm text-muted-foreground">
              Tool parameters: {Object.keys(step.tool_parameters || {}).length} parameter(s)
            </div>
          </div>

          {/* Editable Fields */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="explanation">Explanation</Label>
              <Textarea
                id="explanation"
                placeholder="Describe what this step does..."
                value={formData.explanation_text || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, explanation_text: e.target.value }))}
                rows={3}
                className="max-h-40 overflow-auto"
              />
              <div className="text-xs text-muted-foreground">
                A brief description of what this step accomplishes in the workflow.
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="context">User Context</Label>
              <Textarea
                id="context"
                placeholder="Add any additional context or notes..."
                value={formData.user_context || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, user_context: e.target.value }))}
                rows={2}
                className="max-h-32 overflow-auto"
              />
              <div className="text-xs text-muted-foreground">
                Additional context or notes about this step for future reference.
              </div>
            </div>
          </div>

          {/* Tool Parameters Preview */}
          {step.tool_parameters && Object.keys(step.tool_parameters).length > 0 && (
            <div className="space-y-2">
              <Label>Tool Parameters (Read-only)</Label>
              <div className="p-3 bg-muted/30 rounded border text-sm font-mono max-h-48 overflow-auto">
                <pre className="whitespace-pre-wrap text-xs">
                  {JSON.stringify(step.tool_parameters, null, 2)}
                </pre>
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-2 pt-4 border-t mt-2">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={saving}
          >
            <X className="mr-2 h-4 w-4" />
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={saving}
          >
            {saving ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
