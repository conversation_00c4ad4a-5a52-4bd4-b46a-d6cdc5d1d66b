'use client';

import { useEffect, useState } from 'react';
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  useSidebar,
} from '@/components/ui/sidebar';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Plug, Globe, CircleDashed, Wrench } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { McpIcon } from '@/components/ui/mcp-icon';

export function NavConnections() {
  const { state } = useSidebar();
  const pathname = usePathname();
  const router = useRouter();

  // Helper to determine if a suite is active
  const isActive = (href: string) => pathname?.startsWith(href);

  const suites = [
    {
      name: 'Legacy Tools',
      href: '/workers/suite/legacy',
      icon: <Wrench className="h-4 w-4" />,
    },
    {
      name: 'Web Tools',
      href: '/workers/suite/web',
      icon: <Globe className="h-4 w-4" />,
    },
    {
      name: 'MCP',
      href: '/workers/suite/mcp',
      icon: <McpIcon size={16} />,
    },
  ];

  return (
    <SidebarGroup>
      <div className={`flex items-center w-full my-1 ${state === 'collapsed' ? 'justify-center' : 'justify-between'}`}>
        {state !== 'collapsed' ? (
          <Button
            variant="outline"
            className="justify-start text-left h-9 px-2.5 flex-grow border-border/70 hover:bg-muted/50 data-[state=open]:bg-muted/50"
            asChild
          >
            <Link href="/workers" className="no-underline flex items-center">
              <SidebarGroupLabel className="text-sm font-medium text-foreground">
                Tool Suites
              </SidebarGroupLabel>
            </Link>
          </Button>
        ) : (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                type="button"
                aria-label="Tool Suites"
                className="h-9 w-9 p-0 flex items-center justify-center border-border/70 hover:bg-muted/50"
                onClick={() => router.push('/workers')}
              >
                <CircleDashed className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Tool Suites</TooltipContent>
          </Tooltip>
        )}
      </div>
      <SidebarMenu className="overflow-y-auto max-h-[calc(100vh-200px)] [&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']">
        {suites.map((suite) => (
          <SidebarMenuItem key={suite.name}>
            <SidebarMenuButton
              asChild
              className={isActive(suite.href) ? 'bg-accent text-accent-foreground' : ''}
            >
              <Link href={suite.href} className="flex items-center gap-2">
                {suite.icon}
                <span>{suite.name}</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  );
}
