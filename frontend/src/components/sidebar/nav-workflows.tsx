'use client';

import { useEffect, useState, useRef } from 'react';
import {
  ArrowUpRight,
  MoreHorizontal,
  Trash2,
  Plus,
  Workflow,
  Loader2,
  Leaf
} from "lucide-react"
import { toast } from "sonner"
import { usePathname, useRouter } from "next/navigation"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from "@/components/ui/tooltip"
import { getWorkflows, deleteWorkflow, type Workflow as WorkflowType } from "@/lib/api"
import Link from "next/link"
import { Button } from '@/components/ui/button';

export function NavWorkflows() {
  const { isMobile, state } = useSidebar()
  const [workflows, setWorkflows] = useState<WorkflowType[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [loadingWorkflowId, setLoadingWorkflowId] = useState<string | null>(null)
  const pathname = usePathname()
  const router = useRouter()

  // Helper to sort workflows by updated_at (most recent first)
  const sortWorkflows = (
    workflowsList: WorkflowType[],
  ): WorkflowType[] => {
    return [...workflowsList].sort((a, b) => {
      return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
    });
  };

  // Function to load workflows data
  const loadWorkflows = async (showLoading = true) => {
    try {
      if (showLoading) {
        setIsLoading(true);
      }

      const allWorkflows = await getWorkflows();
      console.log("Workflows loaded:", allWorkflows.length);

      // Set workflows, ensuring consistent sort order
      setWorkflows(sortWorkflows(allWorkflows));
    } catch (err) {
      console.error('Error loading workflows:', err);
      // Set empty workflows array on error
      setWorkflows([]);
    } finally {
      if (showLoading) {
        setIsLoading(false);
      }
    }
  };

  // Load workflows dynamically from the API on initial load
  useEffect(() => {
    loadWorkflows(true);
  }, []);

  // Reset loading state when navigation completes (pathname changes)
  useEffect(() => {
    setLoadingWorkflowId(null);
  }, [pathname]);

  // Function to handle workflow click with loading state
  const handleWorkflowClick = (e: React.MouseEvent<HTMLAnchorElement>, workflowId: string, url: string) => {
    e.preventDefault()
    setLoadingWorkflowId(workflowId)
    router.push(url)
  }

  // Function to handle workflow deletion
  const handleDeleteWorkflow = async (workflowId: string, workflowName: string) => {
    if (!confirm(`Are you sure you want to delete "${workflowName}"?`)) {
      return;
    }

    try {
      await deleteWorkflow(workflowId);
      toast.success('Workflow deleted successfully');
      loadWorkflows(false); // Refresh the list without showing loading state
    } catch (error) {
      console.error('Error deleting workflow:', error);
      toast.error('Failed to delete workflow');
    }
  };

  return (
    <SidebarGroup>
      <div className={`flex items-center w-full my-1 ${state === 'collapsed' ? 'justify-center' : 'justify-between'}`}>
        {state !== 'collapsed' ? (
          <>
            <Button variant="outline" className="justify-start text-left h-9 px-2.5 flex-grow border-border/70 hover:bg-muted/50 data-[state=open]:bg-muted/50" asChild>
              <Link href="/workflows" className="no-underline flex items-center">
                <SidebarGroupLabel className="text-sm font-medium text-foreground">Agent Garden</SidebarGroupLabel>
              </Link>
            </Button>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  type="button"
                  aria-label="Create New Workflow"
                  className="ml-1 h-9 w-9 p-0 flex items-center justify-center border-border/70 hover:bg-muted/50"
                  onClick={() => router.push('/workflows/new')}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Create New Workflow</TooltipContent>
            </Tooltip>
          </>
        ) : (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                type="button"
                aria-label="Agent Garden"
                className="h-9 w-9 p-0 flex items-center justify-center border-border/70 hover:bg-muted/50"
                onClick={() => router.push('/workflows')}
              >
                <Leaf className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Agent Garden</TooltipContent>
          </Tooltip>
        )}
      </div>
      <SidebarMenu className="overflow-y-auto max-h-[calc(100vh-200px)] [&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']">
        {isLoading ? (
          // Show skeleton loaders while loading
          Array.from({ length: 3 }).map((_, index) => (
            <SidebarMenuItem key={`skeleton-${index}`}>
              <SidebarMenuButton>
                <div className="h-4 w-4 bg-sidebar-foreground/10 rounded-md animate-pulse"></div>
                <div className="h-3 bg-sidebar-foreground/10 rounded w-3/4 animate-pulse"></div>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))
        ) : workflows.length > 0 ? (
          // Show all workflows
          <>
            {workflows.map((workflow) => {
              // Check if this workflow is currently active
              const isActive = pathname?.includes(`/workflows/${workflow.workflow_id}`) || false;
              const isWorkflowLoading = loadingWorkflowId === workflow.workflow_id;
              const workflowUrl = `/workflows/${workflow.workflow_id}`;

              return (
                <SidebarMenuItem key={`workflow-${workflow.workflow_id}`}>
                  {state === 'collapsed' ? (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <SidebarMenuButton
                          asChild
                          className={
                            isActive ? 'bg-accent text-accent-foreground' : ''
                          }
                        >
                          <Link
                            href={workflowUrl}
                            onClick={(e) =>
                              handleWorkflowClick(e, workflow.workflow_id, workflowUrl)
                            }
                          >
                            {isWorkflowLoading ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Workflow className="h-4 w-4" />
                            )}
                            <span>{workflow.name}</span>
                          </Link>
                        </SidebarMenuButton>
                      </TooltipTrigger>
                      <TooltipContent>{workflow.name}</TooltipContent>
                    </Tooltip>
                  ) : (
                    <SidebarMenuButton
                      asChild
                      className={
                        isActive
                          ? 'bg-accent text-accent-foreground font-medium'
                          : ''
                      }
                    >
                      <Link
                        href={workflowUrl}
                        onClick={(e) =>
                          handleWorkflowClick(e, workflow.workflow_id, workflowUrl)
                        }
                      >
                        {isWorkflowLoading ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Workflow className="h-4 w-4" />
                        )}
                        <span>{workflow.name}</span>
                      </Link>
                    </SidebarMenuButton>
                  )}
                  {state !== 'collapsed' && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <SidebarMenuAction showOnHover>
                          <MoreHorizontal />
                          <span className="sr-only">More</span>
                        </SidebarMenuAction>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                        className="w-56 rounded-lg"
                        side={isMobile ? 'bottom' : 'right'}
                        align={isMobile ? 'end' : 'start'}
                      >
                        <DropdownMenuItem asChild>
                          <a
                            href={workflowUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            <ArrowUpRight className="text-muted-foreground" />
                            <span>Open in New Tab</span>
                          </a>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() =>
                            handleDeleteWorkflow(
                              workflow.workflow_id,
                              workflow.name,
                            )
                          }
                        >
                          <Trash2 className="text-muted-foreground" />
                          <span>Delete</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </SidebarMenuItem>
              );
            })}
          </>
        ) : (
          // Empty state
          <SidebarMenuItem>
            <SidebarMenuButton className="text-sidebar-foreground/70">
              <Workflow className="h-4 w-4" />
              <span>No workflows yet</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        )}
      </SidebarMenu>
    </SidebarGroup>
  );
}
