import React from 'react';
import { notFound } from 'next/navigation';
import { AlertCircle, ArrowLeft } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ToolIcon } from '@/components/workers/tool-icon';
import { WorkerHeader } from '@/components/workers/header';
import { API_URL } from '@/lib/api-url';
import { cn } from '@/lib/utils';

interface Parameter {
  name: string;
  description: string;
  type: string;
  required: boolean;
}

interface Tool {
  name: string;
  description: string;
  type: string;
  parameters?: {
    properties: Record<string, {
      description: string;
      type: string;
    }>;
    required: string[];
  };
}

function ParameterCard({ param }: { param: Parameter }) {
  return (
    <Card>
      <CardHeader className="space-y-1">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base">{param.name}</CardTitle>
          {param.required && (
            <Badge variant="default" className="text-xs">Required</Badge>
          )}
        </div>
        <CardDescription className="text-sm">{param.type}</CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground">{param.description}</p>
      </CardContent>
    </Card>
  );
}

export default function ToolPage({ params }: { params: { tool: string } }) {
  const [tool, setTool] = React.useState<Tool | null>(null);
  const [isLoading, setIsLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    const fetchTool = async () => {
      try {
        const response = await fetch(`${API_URL}/tools`, {
          headers: { 'Content-Type': 'application/json' },
          mode: 'cors',
          credentials: 'include',
        });
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.detail || `Failed to fetch tools: ${response.status}`);
        }
        const data = await response.json();
        const foundTool = data.find((t: Tool) => t.name === decodeURIComponent(params.tool));
        if (!foundTool) {
          notFound();
        }
        setTool(foundTool);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch tool details');
      } finally {
        setIsLoading(false);
      }
    };
    fetchTool();
  }, [params.tool]);

  if (error) {
    return (
      <div className="flex flex-col h-full">
        <WorkerHeader
          title="Tool Details"
          description="View and understand tool capabilities."
          icon={<ToolIcon toolName={decodeURIComponent(params.tool)} size={24} />}
          showBackButton
        />
        <div className="flex-1 p-4 md:p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription className="break-words">{error}</AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  const parameters: Parameter[] = tool?.parameters
    ? Object.entries(tool.parameters.properties).map(([name, details]) => ({
        name,
        description: details.description,
        type: details.type,
        required: tool.parameters?.required.includes(name) || false,
      }))
    : [];

  return (
    <div className="flex flex-col h-full">
      <WorkerHeader
        title={isLoading ? 'Loading...' : tool?.name || 'Tool Details'}
        description={isLoading ? 'Loading tool details...' : tool?.description || ''}
        icon={<ToolIcon toolName={decodeURIComponent(params.tool)} size={24} />}
        showBackButton
      />
      <div className="flex-1 p-4 md:p-6">
        {isLoading ? (
          <div className="space-y-6">
            <Skeleton className="h-8 w-[200px]" />
            <Skeleton className="h-[100px] w-full" />
            <div className="grid gap-4 md:grid-cols-2">
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-[120px]" />
              ))}
            </div>
          </div>
        ) : tool ? (
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="parameters">Parameters</TabsTrigger>
              <TabsTrigger value="examples">Examples</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Description</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground whitespace-pre-wrap">{tool.description}</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Quick Start</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="rounded-md bg-muted p-4">
                    <pre className="text-sm">
                      <code>{`// Example usage of ${tool.name}\n// Coming soon...`}</code>
                    </pre>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="parameters" className="space-y-6">
              {parameters.length === 0 ? (
                <Card>
                  <CardContent className="pt-6">
                    <p className="text-muted-foreground">This tool has no parameters.</p>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid gap-4 md:grid-cols-2">
                  {parameters.map((param) => (
                    <ParameterCard key={param.name} param={param} />
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="examples" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Usage Examples</CardTitle>
                  <CardDescription>
                    Learn how to use this tool effectively with practical examples.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">Examples coming soon...</p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        ) : null}
      </div>
    </div>
  );
}
