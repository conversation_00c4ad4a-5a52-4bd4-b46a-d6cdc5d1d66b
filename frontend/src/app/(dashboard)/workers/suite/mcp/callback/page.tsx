"use client";

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { Loader2, Check, X, AlertTriangle } from 'lucide-react';

export default function MCPOAuthCallbackPage() {
  const searchParams = useSearchParams();
  const success = searchParams.get('success');
  const error = searchParams.get('error');
  const service = searchParams.get('service');
  const connectionId = searchParams.get('connection_id');

  const [countdown, setCountdown] = useState(3);

  useEffect(() => {
    // Handle Composio OAuth callback results
    if (success === 'true') {
      // OAuth completed successfully - notify parent window
      window.parent?.postMessage({
        type: 'OAUTH_SUCCESS',
        service: service,
        connectionId: connectionId,
        message: `Successfully connected to ${service}`
      }, window.location.origin);

      // Auto-close after countdown
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            window.close();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    } else if (error) {
      // OAuth failed - notify parent window
      window.parent?.postMessage({
        type: 'OAUTH_ERROR',
        error: error,
        message: error
      }, window.location.origin);

      // Auto-close after countdown
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            window.close();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [success, error, service, connectionId]);

  if (success === 'true') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-emerald-50">
        <div className="bg-white p-8 rounded-lg shadow-lg text-center max-w-md">
          <div className="mb-4">
            <Check className="w-16 h-16 text-green-500 mx-auto" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Connection Successful!
          </h1>
          <p className="text-gray-600 mb-4">
            {service ? `Successfully connected to ${service.charAt(0).toUpperCase() + service.slice(1)}` : 'Your service has been connected successfully'}
          </p>
          <p className="text-sm text-gray-500">
            This window will close automatically in {countdown} seconds...
          </p>
          <button
            onClick={() => window.close()}
            className="mt-4 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
          >
            Close Now
          </button>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-rose-50">
        <div className="bg-white p-8 rounded-lg shadow-lg text-center max-w-md">
          <div className="mb-4">
            <X className="w-16 h-16 text-red-500 mx-auto" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Connection Failed
          </h1>
          <p className="text-gray-600 mb-4">
            {error}
          </p>
          <p className="text-sm text-gray-500 mb-4">
            This window will close automatically in {countdown} seconds...
          </p>
          <div className="space-y-2">
            <button
              onClick={() => window.close()}
              className="w-full px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
            >
              Close Now
            </button>
            <button
              onClick={() => {
                window.parent?.postMessage({
                  type: 'RETRY_CONNECTION',
                  service: service
                }, window.location.origin);
                window.close();
              }}
              className="w-full px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Loading state - should not normally be seen with new pattern
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-50">
      <div className="bg-white p-8 rounded-lg shadow-lg text-center max-w-md">
        <div className="mb-4">
          <Loader2 className="w-16 h-16 text-blue-500 mx-auto animate-spin" />
        </div>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Processing OAuth...
        </h1>
        <p className="text-gray-600">
          Please wait while we complete your authentication...
        </p>
      </div>
    </div>
  );
}
