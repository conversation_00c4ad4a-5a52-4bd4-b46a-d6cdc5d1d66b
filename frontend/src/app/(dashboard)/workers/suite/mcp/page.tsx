'use client';
import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { <PERSON>ertCircle, Check, X, Loader2, Trash2, Mail, Folder, Book, Calendar, MessageCircle, Plug, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { Input } from '@/components/ui/input';
import { API_URL } from '@/lib/api-url';
import { WorkerHeader } from '@/components/workers/header';
import { ToolIcon } from '@/components/workers/tool-icon';
import { cn, truncateDescription } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';
import { createClient } from '@/lib/supabase/client';
import { SiG<PERSON>, SiNotion, SiGoogledrive, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'react-icons/si';

interface Tool {
  name: string;
  description: string;
  type: string;
  parameters?: {
    properties: Record<string, any>;
    required: string[];
  };
}

interface ServiceConfig {
  name: string;
  display_name: string;
  description: string;
  icon: string;
  category: 'email' | 'documents' | 'communication' | 'development' | 'other';
}

interface Connection {
  id: string;
  user_id: string;
  service_name: string;
  composio_connection_id: string;
  mcp_url: string | null;
  status: string;
  created_at: string;
  updated_at: string;
}

interface UnifiedService {
  name: string;
  display_name: string;
  description: string;
  icon: string;
  category: string;
  is_connected: boolean;
  connection?: Connection;
  available_tools?: Tool[];
  status: 'available' | 'connected' | 'connecting' | 'disconnecting';
}

// Available services for connection
const AVAILABLE_SERVICES: ServiceConfig[] = [
  {
    name: 'gmail',
    display_name: 'Gmail',
    description: 'Send emails, manage your inbox, and organize your communications efficiently.',
    icon: 'gmail',
    category: 'email'
  },
  {
    name: 'googledrive',
    display_name: 'Google Drive',
    description: 'Access, manage, and share files stored in your Google Drive account.',
    icon: 'google-drive',
    category: 'documents'
  },
  {
    name: 'notion',
    display_name: 'Notion',
    description: 'Create, edit, and manage pages, databases, and content in your Notion workspace.',
    icon: 'notion',
    category: 'documents'
  },
  {
    name: 'slack',
    display_name: 'Slack',
    description: 'Send messages, manage channels, and coordinate team communications.',
    icon: 'slack',
    category: 'communication'
  },
  {
    name: 'apollo',
    display_name: 'Apollo GraphQL',
    description: 'Query and manage GraphQL schemas and data sources.',
    icon: 'apollo',
    category: 'development'
  }
];

const SERVICE_ICONS = {
  gmail: SiGmail,
  notion: SiNotion,
  'google-drive': SiGoogledrive,
  slack: SiSlack,
  apollo: SiGithub, // Using GitHub icon as placeholder for Apollo
};

export default function MCPSuitePage() {
  const [services, setServices] = useState<UnifiedService[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [connectingService, setConnectingService] = useState<string | null>(null);
  const [disconnectingConnection, setDisconnectingConnection] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const { toast } = useToast();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      // Initialize services from available services list
      let unifiedServices: UnifiedService[] = AVAILABLE_SERVICES.map(service => ({
        ...service,
        is_connected: false,
        status: 'available' as const,
        available_tools: []
      }));

      // Load existing tools from the streaming endpoint (old MCP tools that are still active)
      try {
        const toolsResponse = await fetch(`${API_URL}/tools`);
        if (toolsResponse.ok) {
          const reader = toolsResponse.body?.getReader();
          const decoder = new TextDecoder();
          let allTools: Tool[] = [];

          if (reader) {
            while (true) {
              const { done, value } = await reader.read();
              if (done) break;

              const chunk = decoder.decode(value);
              const lines = chunk.split('\n').filter(line => line.trim());

              for (const line of lines) {
                if (line.startsWith('data: ')) {
                  try {
                    const data = JSON.parse(line.slice(6));
                    if (data.tools && Array.isArray(data.tools)) {
                      allTools.push(...data.tools);
                    }
                  } catch (parseErr) {
                    console.warn('Failed to parse SSE data:', parseErr);
                  }
                }
              }
            }
          }

          // Filter for MCP tools and match them to services
          const mcpTools = allTools.filter(tool =>
            tool.name.includes('mcp') ||
            AVAILABLE_SERVICES.some(service =>
              tool.name.toLowerCase().includes(service.name.toLowerCase())
            )
          );

          // Update services with their available tools
          unifiedServices = unifiedServices.map(service => {
            const relatedTools = mcpTools.filter(tool =>
              tool.name.toLowerCase().includes(service.name.toLowerCase())
            );
            return {
              ...service,
              available_tools: relatedTools
            };
          });
        }
      } catch (toolsError) {
        console.warn('Failed to load tools:', toolsError);
      }

      // Load user connections if authenticated
      if (session?.access_token) {
        try {
          const connectionsResponse = await fetch(`${API_URL}/v1/connections`, {
            headers: {
              'Authorization': `Bearer ${session.access_token}`,
            },
          });

          if (connectionsResponse.ok) {
            const connectionsData = await connectionsResponse.json();
            const connections: Connection[] = connectionsData.connections || [];

            // Update services with connection status
            unifiedServices = unifiedServices.map(service => {
              const connection = connections.find(
                conn => conn.service_name === service.name && conn.status === 'active'
              );

              if (connection) {
                return {
                  ...service,
                  is_connected: true,
                  connection,
                  status: 'connected' as const
                };
              }

              return service;
            });

          } else if (connectionsResponse.status !== 404) {
            console.warn('Failed to load user connections:', connectionsResponse.status);
          }
        } catch (connectionError) {
          console.warn('Connection endpoints not available yet:', connectionError);
        }
      }

      setServices(unifiedServices);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch data');
    } finally {
      setIsLoading(false);
    }
  };

  const initiateConnection = async (serviceName: string) => {
    try {
      setConnectingService(serviceName);

      // Update service status optimistically
      setServices(prev => prev.map(service =>
        service.name === serviceName
          ? { ...service, status: 'connecting' as const }
          : service
      ));

      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.access_token) {
        toast({
          title: "Authentication Required",
          description: "Please sign in to connect services",
          variant: "destructive"
        });
        return;
      }

      // Detect Safari and Google services for specific guidance
      const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
      const isGoogleService = serviceName.toLowerCase().includes('gmail') || serviceName.toLowerCase().includes('google');

      // Initiate connection following Composio's documented pattern
      const response = await fetch(`${API_URL}/v1/connections/initiate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({
          service_name: serviceName,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to initiate connection');
      }

      const { redirect_url, state } = await response.json();

      if (!redirect_url || !state) {
        throw new Error('Invalid connection response - missing redirect_url or state');
      }

      // Show Safari-specific guidance for Google services
      if (isSafari && isGoogleService) {
        toast({
          title: "Safari Popup Notice",
          description: "Safari may block the OAuth popup. If nothing happens, check the address bar for a popup blocker icon and allow popups for this site.",
        });
      }

      // Open OAuth popup following Composio's pattern
      const popup = window.open(
        redirect_url,
        'oauth_popup',
        'width=500,height=600,scrollbars=yes,resizable=yes'
      );

      if (!popup) {
        // Popup was blocked
        toast({
          title: "Popup Blocked",
          description: isSafari
            ? "Safari blocked the popup. Please allow popups for this site in Safari's address bar, then try again."
            : "Your browser blocked the popup. Please allow popups for this site and try again.",
          variant: "destructive",
        });

        setServices(prev => prev.map(service =>
          service.name === serviceName
            ? { ...service, status: 'available' as const }
            : service
        ));
        return;
      }

      // Show different messages for Google vs other services
      if (isGoogleService) {
        toast({
          title: "Google OAuth In Progress",
          description: "Google may show additional security screens. This is normal for Gmail/Drive access. Complete all prompts to finish connecting.",
        });
      }

      // Use Composio's documented wait_until_active pattern via our backend
      try {
        console.log(`⏳ Starting wait_until_active pattern for state: ${state}`);

        // Call our backend's wait-activation endpoint which uses Composio's wait_until_active
        const activationResponse = await fetch(`${API_URL}/v1/connections/wait-activation`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`,
          },
          body: JSON.stringify({
            state: state,
            timeout: 120 // 2 minutes timeout
          }),
        });

        const activationResult = await activationResponse.json();

        // Close popup regardless of result
        popup?.close();

        if (activationResult.success) {
          // Connection successful using Composio's wait_until_active pattern
          toast({
            title: "Connected Successfully!",
            description: `${serviceName} is now connected and ready to use.`,
          });

          // Update service status with complete connection data
          setServices(prev => prev.map(service =>
            service.name === serviceName
              ? {
                  ...service,
                  status: 'connected' as const,
                  is_connected: true,
                  connection: {
                    id: activationResult.connection_id || '',
                    service_name: serviceName,
                    status: 'active',
                    mcp_url: activationResult.mcp_url || '',
                    user_id: session.user.id,
                    composio_connection_id: activationResult.connection_id || '',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                  }
                }
              : service
          ));

          // Refresh connections data to get complete info
          loadData();
        } else {
          // Handle activation failure
          const errorMessage = activationResult.error === 'timeout'
            ? isGoogleService
              ? "Google OAuth process took too long. This might be due to multiple security prompts. Please try again."
              : "OAuth process took too long. Please try again."
            : activationResult.message || "Failed to establish connection";

          toast({
            title: "Connection Failed",
            description: errorMessage,
            variant: "destructive"
          });

          setServices(prev => prev.map(service =>
            service.name === serviceName
              ? { ...service, status: 'available' as const }
              : service
          ));
        }

      } catch (activationError) {
        console.error('Activation wait error:', activationError);
        popup?.close();

        toast({
          title: "Connection Failed",
          description: "Failed to complete connection. Please try again.",
          variant: "destructive"
        });

        setServices(prev => prev.map(service =>
          service.name === serviceName
            ? { ...service, status: 'available' as const }
            : service
        ));
      }

    } catch (error) {
      console.error('Connection initiation error:', error);

      toast({
        title: "Connection Failed",
        description: error instanceof Error ? error.message : "Failed to initiate connection",
        variant: "destructive"
      });

      setServices(prev => prev.map(service =>
        service.name === serviceName
          ? { ...service, status: 'available' as const }
          : service
      ));
    } finally {
      setConnectingService(null);
    }
  };

  const disconnectService = async (connectionId: string, serviceName: string) => {
    try {
      setDisconnectingConnection(connectionId);

      // Update service status optimistically
      setServices(prev => prev.map(service =>
        service.name === serviceName
          ? { ...service, status: 'disconnecting' as const }
          : service
      ));

      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.access_token) {
        throw new Error('Authentication required');
      }

      const response = await fetch(`${API_URL}/v1/connections/${connectionId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to disconnect service');
      }

      toast({
        title: "Disconnected",
        description: `Successfully disconnected ${serviceName}`,
      });

      await loadData(); // Reload data

    } catch (error) {
      console.error('Failed to disconnect service:', error);
      toast({
        title: "Disconnect Failed",
        description: "Failed to disconnect the service",
        variant: "destructive"
      });

      // Reset service status on error
      setServices(prev => prev.map(service =>
        service.name === serviceName
          ? { ...service, status: service.is_connected ? 'connected' : 'available' }
          : service
      ));
    } finally {
      setDisconnectingConnection(null);
    }
  };

  const getStatusBadge = (service: UnifiedService) => {
    switch (service.status) {
      case 'connected':
        return (
          <Badge variant="default" className="bg-blue-100 text-blue-800 border-blue-200">
            <Check className="w-3 h-3 mr-1" />
            Connected
          </Badge>
        );
      case 'connecting':
        return (
          <Badge variant="default" className="bg-blue-100 text-blue-800 border-blue-200">
            <Loader2 className="w-3 h-3 mr-1 animate-spin" />
            Connecting
          </Badge>
        );
      case 'disconnecting':
        return (
          <Badge variant="secondary" className="bg-gray-100 text-gray-600">
            <Loader2 className="w-3 h-3 mr-1 animate-spin" />
            Disconnecting
          </Badge>
        );
      default:
        return null; // No badge for available state
    }
  };

  const getServiceActions = (service: UnifiedService) => {
    if (service.is_connected && service.connection) {
      return (
        <Button
          variant="outline"
          size="sm"
          onClick={() => disconnectService(service.connection!.id, service.display_name)}
          disabled={service.status === 'disconnecting'}
          className="w-full h-8 text-xs hover:bg-destructive/10 hover:text-destructive hover:border-destructive/20"
        >
          {service.status === 'disconnecting' ? (
            <>
              <Loader2 className="mr-1.5 h-3 w-3 animate-spin" />
              Disconnecting
            </>
          ) : (
            <>
              <Trash2 className="mr-1.5 h-3 w-3" />
              Disconnect
            </>
          )}
        </Button>
      );
    }

    return (
      <Button
        onClick={() => initiateConnection(service.name)}
        disabled={service.status === 'connecting'}
        className="w-full h-8 text-xs bg-primary hover:bg-primary/90 text-primary-foreground shadow-sm"
        size="sm"
      >
        {service.status === 'connecting' ? (
          <>
            <Loader2 className="mr-1.5 h-3 w-3 animate-spin" />
            Connecting
          </>
        ) : (
          <>
            <Plug className="mr-1.5 h-3 w-3" />
            Connect
          </>
        )}
      </Button>
    );
  };

  if (error) {
    return (
      <div className="flex flex-col h-full">
        <WorkerHeader
          title="MCP Tools & Connections"
          description="Connect your accounts and manage MCP tools for powerful integrations."
          icon={<ToolIcon toolName="mcp" size={24} />}
          showBackButton
        />
        <div className="flex-1 p-4 md:p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription className="break-words">{error}</AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <WorkerHeader
        title="MCP Tools & Connections"
        description="Connect your accounts to enable powerful MCP tools for Atlas. Each connection is secure and private to your account."
        icon={<ToolIcon toolName="mcp" size={24} />}
        showBackButton
      />
      <div className="flex-1 p-4 md:p-6">
        {/* Search Bar */}
        <div className="mb-6">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search services..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {isLoading ? (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="rounded-lg border p-4 h-[140px] flex flex-col">
                <div className="flex flex-col h-full space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Skeleton className="h-9 w-9 rounded-lg" />
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-20" />
                      </div>
                    </div>
                    <Skeleton className="h-5 w-14" />
                  </div>
                  <div className="flex-1"></div>
                  <Skeleton className="h-8 w-full" />
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {services
              .filter(service =>
                service.display_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                service.description.toLowerCase().includes(searchQuery.toLowerCase())
              )
              .map((service) => {
              const IconComponent = SERVICE_ICONS[service.icon as keyof typeof SERVICE_ICONS] || Plug;
              const toolCount = service.available_tools?.length || 0;

              return (
                <div
                  key={service.name}
                  className={cn(
                    "group relative rounded-lg border p-4 transition-all h-[140px] flex flex-col",
                    "hover:bg-muted/50 hover:shadow-md hover:-translate-y-0.5"
                  )}
                >
                  <div className="flex flex-col h-full space-y-3">
                    {/* Header with icon, name, and status */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 rounded-lg bg-primary/10">
                          <IconComponent className="h-5 w-5 text-primary" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-base leading-none">
                            {service.display_name}
                          </h3>
                        </div>
                      </div>
                      {getStatusBadge(service)}
                    </div>




                    {/* Spacer to push actions to bottom */}
                    <div className="flex-1"></div>

                    {/* Actions */}
                    <div className="flex gap-2 mt-auto">
                      {getServiceActions(service)}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* Empty State */}
        {!isLoading && services.filter(service =>
          service.display_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          service.description.toLowerCase().includes(searchQuery.toLowerCase())
        ).length === 0 && (
                      <div className="flex flex-col items-center justify-center p-8 text-center border rounded-lg">
              <ToolIcon toolName="mcp" size={48} className="text-muted-foreground mb-4" />
              <h2 className="text-xl font-semibold mb-2">
                {searchQuery ? 'No Services Found' : 'No MCP Services Available'}
              </h2>
              <p className="text-muted-foreground max-w-md">
                {searchQuery
                  ? `No services match "${searchQuery}". Try a different search term.`
                  : 'MCP services will appear here once they are configured. Contact your administrator if you expected to see services here.'
                }
              </p>
            </div>
        )}
      </div>
    </div>
  );
}
