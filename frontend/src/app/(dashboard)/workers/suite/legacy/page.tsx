'use client';
import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { API_URL } from '@/lib/api-url';
import { WorkerHeader } from '@/components/workers/header';
import { ToolIcon } from '@/components/workers/tool-icon';
import { cn, truncateDescription } from '@/lib/utils';

interface Tool {
  name: string;
  description: string;
  type: string;
  parameters?: {
    properties: Record<string, any>;
    required: string[];
  };
}

// Legacy tool names and their display order
const LEGACY_TOOL_NAMES = [
  'ask',
  'complete',
  'execute_data_provider_call',
  'get_data_provider_endpoints',
];

export default function LegacySuitePage() {
  const [tools, setTools] = useState<Tool[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTools = async () => {
      try {
        const response = await fetch(`${API_URL}/tools`, {
          headers: { 'Content-Type': 'application/json' },
          mode: 'cors',
          credentials: 'include',
        });
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.detail || `Failed to fetch tools: ${response.status}`);
        }
        const data = await response.json();

        // Filter and sort legacy tools
        const legacyTools = data.filter((tool: Tool) => {
          const normalizedName = tool.name.toLowerCase();
          return (
            // Check if the tool name starts with any of our legacy tool prefixes
            LEGACY_TOOL_NAMES.some(prefix => normalizedName.startsWith(prefix.toLowerCase()))
          );
        });

        // Sort tools by putting exact matches first, then prefix matches
        legacyTools.sort((a: Tool, b: Tool) => {
          const aName = a.name.toLowerCase();
          const bName = b.name.toLowerCase();

          // Helper function to get the index of the first matching prefix
          const getPrefixIndex = (name: string) => {
            const index = LEGACY_TOOL_NAMES.findIndex(prefix =>
              name.startsWith(prefix.toLowerCase())
            );
            return index === -1 ? Infinity : index;
          };

          return getPrefixIndex(aName) - getPrefixIndex(bName);
        });

        setTools(legacyTools);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch tools');
      } finally {
        setIsLoading(false);
      }
    };
    fetchTools();
  }, []);

  if (error) {
    return (
      <div className="flex flex-col h-full">
        <WorkerHeader
          title="Legacy Tools"
          description="Access and manage legacy data provider tools."
          icon={<ToolIcon toolName="legacy" size={24} />}
          showBackButton
        />
        <div className="flex-1 p-4 md:p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription className="break-words">{error}</AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <WorkerHeader
        title="Legacy Tools"
        description="Access and manage legacy data provider tools."
        icon={<ToolIcon toolName="legacy" size={24} />}
        showBackButton
      />
      <div className="flex-1 p-4 md:p-6">
        {isLoading ? (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="rounded-lg border p-6">
                <div className="flex items-start gap-4">
                  <Skeleton className="h-10 w-10 rounded-lg" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-5 w-3/4" />
                    <Skeleton className="h-4 w-full" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : tools.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-8 text-center border rounded-lg">
            <ToolIcon toolName="legacy" size={48} className="text-muted-foreground mb-4" />
            <h2 className="text-xl font-semibold mb-2">No Legacy Tools Found</h2>
            <p className="text-muted-foreground max-w-md">
              No legacy tools are currently available in this suite.
            </p>
          </div>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {tools.map((tool) => (
              <Link
                key={tool.name}
                href={`/workers?tool=${encodeURIComponent(tool.name)}`}
                className={cn(
                  "group relative rounded-lg border p-6 hover:bg-muted/50 transition-all",
                  "hover:shadow-md hover:-translate-y-0.5"
                )}
              >
                <div className="flex items-start gap-4">
                  <ToolIcon toolName={tool.name} size={24} containerClassName="shrink-0" />
                  <div className="space-y-2">
                    <h3 className="font-semibold leading-none tracking-tight">
                      {tool.name}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {truncateDescription(tool.description || 'No description provided', 120)}
                    </p>
                  </div>
                </div>
                <span className="absolute inset-0 rounded-lg ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2" />
              </Link>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
