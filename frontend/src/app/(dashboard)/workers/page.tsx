'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { PlusCircle, MessagesSquare, AlertCircle, ArrowLeft, Wrench, Globe, Plug, CircleDashed } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { useRouter } from 'next/navigation';
import { API_URL } from '@/lib/api-url';
import { cn } from '@/lib/utils';
import { WorkerHeader } from '@/components/workers/header';
import { McpIcon } from '@/components/ui/mcp-icon';
import { ToolIcon } from '@/components/workers/tool-icon';

// Define the tool/worker interface
interface Tool {
  name: string;
  description: string;
  type: string;
  parameters?: {
    properties: Record<string, any>;
    required: string[];
  };
}

// Define the tool suites
const TOOL_SUITES = [
  {
    key: 'legacy',
    name: 'Legacy Tools',
    description: 'Basic built-in tools for automation and scripting.',
    icon: Wrench,
    route: '/workers/suite/legacy',
  },
  {
    key: 'web',
    name: 'Web Tools',
    description: 'Specialized tools for web automation and scraping.',
    icon: Globe,
    route: '/workers/suite/web',
  },
  {
    key: 'mcp',
    name: 'MCP',
    description: 'Model Control Protocol (MCP) tools for AI integration and automation.',
    icon: ({ className, size = 24 }) => (
      <McpIcon className={className} size={size} />
    ),
    route: '/workers/suite/mcp',
  },
];

export default function WorkersPage() {
  const [tools, setTools] = useState<Tool[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    const fetchTools = async () => {
      try {
        const response = await fetch(`${API_URL}/tools`, {
          headers: {
            'Content-Type': 'application/json',
          },
          mode: 'cors',
          credentials: 'include',
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.detail || `Failed to fetch tools: ${response.status}`);
        }

        const data = await response.json();
        setTools(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch tools');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTools();
  }, []);

  if (error) {
    return (
      <div className="space-y-6 max-w-full">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription className="break-words">{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  // Always show the list of all tool suites, never show tool details
  return (
    <div className="flex flex-col h-full">
      <WorkerHeader
        title="Tool Suites"
        description="Explore and manage your tool suites for automation and integrations."
        icon={<CircleDashed className="h-6 w-6 text-foreground/70" />}
      />
      <div className="flex-1 p-4 md:p-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {TOOL_SUITES.map((suite) => (
            <Link
              key={suite.key}
              href={suite.route}
              className={cn(
                "group relative rounded-lg border p-6 hover:bg-muted/50 transition-all",
                "hover:shadow-md hover:-translate-y-0.5"
              )}
            >
              <div className="flex items-start gap-4">
                <div className="rounded-lg border bg-background p-2 flex items-center justify-center min-w-[40px] min-h-[40px]">
                  <suite.icon className="h-6 w-6 text-foreground/70" />
                </div>
                <div className="space-y-2">
                  <h3 className="font-semibold leading-none tracking-tight">
                    {suite.name}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {suite.description}
                  </p>
                </div>
              </div>
              <span className="absolute inset-0 rounded-lg ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2" />
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}
