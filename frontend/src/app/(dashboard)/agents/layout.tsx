import { Metadata } from 'next';

export const metadata = {
  title: 'Agent Conversation | Atlas',
  description: 'Interactive agent conversation powered by Atlas',
  openGraph: {
    title: 'Agent Conversation | Atlas',
    description: 'Interactive agent conversation powered by Atlas',
  },
};

export default function AgentsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
