'use client';

import { useEffect, useState } from 'react';
import { Plus, FileText, Play, Trash2, Calendar, Leaf } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { createClient } from '@/lib/supabase/client';
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';
import { WorkerHeader } from '@/components/workers/header';
import { useRouter } from 'next/navigation';

interface WorkflowStep {
  step_id: string;
  step_order: number;
  tool_name: string;
  tool_parameters: Record<string, any>;
  explanation_text?: string;
  user_context?: string;
  parameter_config?: Record<string, any>;
}

interface Workflow {
  workflow_id: string;
  name: string;
  description: string;
  status: string;
  execution_count: number;
  source_thread_id?: string;
  steps: WorkflowStep[];
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export default function WorkflowsPage() {
  const router = useRouter();
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchWorkflows = async () => {
    try {
      setLoading(true);
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.access_token) {
        toast.error('Please log in to view workflows');
        return;
      }

      const response = await fetch('/api/workflows', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch workflows');
      }

      const data = await response.json();
      setWorkflows(data.workflows || []);
    } catch (error) {
      console.error('Error fetching workflows:', error);
      toast.error('Failed to load workflows');
    } finally {
      setLoading(false);
    }
  };

  const deleteWorkflow = async (workflowId: string, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click
    try {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.access_token) {
        toast.error('Please log in to delete workflows');
        return;
      }

      const response = await fetch(`/api/workflows/${workflowId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to delete workflow');
      }

      toast.success('Workflow deleted successfully');
      fetchWorkflows(); // Refresh the list
    } catch (error) {
      console.error('Error deleting workflow:', error);
      toast.error('Failed to delete workflow');
    }
  };

  const executeWorkflow = async (workflowId: string, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click
    try {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.access_token) {
        toast.error('Please log in to execute workflows');
        return;
      }

      const response = await fetch(`/api/workflows/${workflowId}/execute`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}),
      });

      if (!response.ok) {
        throw new Error('Failed to execute workflow');
      }

      const data = await response.json();
      toast.info(data.message || 'Workflow execution started');
    } catch (error) {
      console.error('Error executing workflow:', error);
      toast.error('Failed to execute workflow');
    }
  };

  const handleCardClick = (workflowId: string) => {
    router.push(`/workflows/${workflowId}`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950/50 dark:text-green-400 dark:border-green-800';
      case 'archived': return 'bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-950/50 dark:text-gray-400 dark:border-gray-800';
      case 'template': return 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950/50 dark:text-blue-400 dark:border-blue-800';
      default: return 'bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-950/50 dark:text-gray-400 dark:border-gray-800';
    }
  };

  useEffect(() => {
    fetchWorkflows();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <WorkerHeader
        title="Agent Garden"
        description="Cultivate and nurture your automated agent workflows extracted from chat conversations"
        icon={<Leaf className="h-7 w-7 text-foreground/70" />}
        action={
          <Button onClick={() => router.push('/workflows/new')}>
            <Plus className="mr-2 h-4 w-4" />
            Create New Workflow
          </Button>
        }
      />

      <div className="flex-1 p-4 md:p-6">
        {workflows.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-8 text-center border border-border/50 rounded-xl shadow-sm bg-background">
            <FileText className="h-12 w-12 text-muted-foreground mb-4" />
            <h2 className="text-xl font-semibold mb-2">Your garden awaits</h2>
            <p className="text-muted-foreground max-w-md mb-4">
              Start cultivating your agent garden by creating your first workflow.
            </p>
            <Button onClick={() => router.push('/workflows/new')}>
              <Plus className="mr-2 h-4 w-4" />
              Create New Workflow
            </Button>
          </div>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {workflows.map((workflow) => (
              <Card
                key={workflow.workflow_id}
                className="group cursor-pointer border border-border/50 bg-background hover:bg-accent/50 transition-all duration-200 hover:shadow-md hover:border-border"
                onClick={() => handleCardClick(workflow.workflow_id)}
              >
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <Badge variant="outline" className={`${getStatusColor(workflow.status)} text-xs font-medium`}>
                      {workflow.status}
                    </Badge>
                    <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 hover:bg-accent"
                        onClick={(e) => executeWorkflow(workflow.workflow_id, e)}
                      >
                        <Play className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 hover:bg-destructive/10 hover:text-destructive"
                        onClick={(e) => deleteWorkflow(workflow.workflow_id, e)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <CardTitle className="text-lg font-semibold line-clamp-2 group-hover:text-primary transition-colors duration-200">
                      {workflow.name}
                    </CardTitle>
                    <CardDescription className="text-sm text-muted-foreground line-clamp-2">
                      {workflow.description}
                    </CardDescription>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <div className="flex items-center">
                      <Calendar className="mr-1 h-3 w-3" />
                      {formatDistanceToNow(new Date(workflow.created_at))} ago
                    </div>
                    <div className="text-right">
                      {workflow.steps?.length || 0} steps
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
