'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { WorkerHeader } from '@/components/workers/header';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Search, MessageSquare, Calendar, ArrowRight, Loader2, Leaf, Plus, FileText, Sparkles, Wand2 } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { createClient } from '@/lib/supabase/client';
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';
import { getThreads, getProjects } from '@/lib/api';

interface Thread {
  thread_id: string;
  title: string;
  created_at: string;
  updated_at: string;
  message_count?: number;
  preview?: string;
}

export default function NewWorkflowPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('extract');
  const [loading, setLoading] = useState(true);
  const [extracting, setExtracting] = useState(false);
  const [threads, setThreads] = useState<Thread[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedThread, setSelectedThread] = useState<Thread | null>(null);

  // Manual creation form state
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');

  // Fetch threads when component mounts or when tab changes to extract
  useEffect(() => {
    if (activeTab === 'extract') {
      fetchThreads();
    }
  }, [activeTab]);

  const fetchThreads = async () => {
    try {
      setLoading(true);
      const projects = await getProjects();

      if (projects.length === 0) {
        setThreads([]);
        return;
      }

      const allThreads = await getThreads();
      const projectsById = new Map();
      projects.forEach((project) => {
        projectsById.set(project.id, project);
      });

      const transformedThreads: Thread[] = allThreads.map((thread) => {
        const project = projectsById.get(thread.project_id);
        return {
          thread_id: thread.thread_id,
          title: project?.name || 'Untitled Conversation',
          created_at: thread.created_at,
          updated_at: thread.updated_at,
          message_count: 0,
          preview: project?.description || '',
        };
      });

      setThreads(transformedThreads);
    } catch (error) {
      console.error('Error fetching threads:', error);
      toast.error('Failed to load conversations');
    } finally {
      setLoading(false);
    }
  };

  const extractWorkflow = async () => {
    if (!selectedThread) return;

    try {
      setExtracting(true);
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.access_token) {
        toast.error('Please log in to extract workflows');
        return;
      }

      const response = await fetch('/api/workflows/extract', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          thread_id: selectedThread.thread_id,
          model_name: 'gpt-4o-mini',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to extract workflow');
      }

      const data = await response.json();

      if (data.workflows && data.workflows.length > 0) {
        toast.success(`Successfully extracted ${data.workflows.length} workflow(s)!`);
        router.push('/workflows');
      } else {
        toast.info('No workflows found in this conversation');
      }
    } catch (error) {
      console.error('Error extracting workflow:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to extract workflow');
    } finally {
      setExtracting(false);
    }
  };

  const createWorkflow = async () => {
    if (!name.trim()) {
      toast.error('Please enter a workflow name');
      return;
    }

    try {
      setLoading(true);
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.access_token) {
        toast.error('Please log in to create workflows');
        return;
      }

      const response = await fetch('/api/workflows', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          description,
          steps: [],
          metadata: {},
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create workflow');
      }

      toast.success('Workflow created successfully!');
      router.push('/workflows');
    } catch (error) {
      console.error('Error creating workflow:', error);
      toast.error('Failed to create workflow');
    } finally {
      setLoading(false);
    }
  };

  const filteredThreads = threads.filter(thread =>
    thread.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    thread.preview?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      <WorkerHeader
        title="Create New Workflow"
        description="Transform conversations into automated workflows"
        icon={<Leaf className="h-6 w-6" />}
      />

      <div className="space-y-8">
        <Tabs defaultValue="extract" className="pt-2" onValueChange={(value) => {
          setActiveTab(value);
        }}>
          <div className="flex justify-center">
            <TabsList className="grid w-full max-w-md grid-cols-2 h-14 p-1 bg-background/50 dark:bg-background/10 border border-border/30">
              <TabsTrigger
                value="extract"
                className="flex items-center gap-2.5 px-4 py-2.5 text-sm font-medium text-muted-foreground data-[state=active]:text-foreground data-[state=active]:font-semibold dark:text-muted-foreground dark:data-[state=active]:text-foreground data-[state=active]:bg-background dark:data-[state=active]:bg-background/90 transition-all duration-200 rounded-md"
              >
                <Sparkles className="h-4 w-4" />
                Extract from Chat
              </TabsTrigger>
              <TabsTrigger
                value="manual"
                className="flex items-center gap-2.5 px-4 py-2.5 text-sm font-medium text-muted-foreground data-[state=active]:text-foreground data-[state=active]:font-semibold dark:text-muted-foreground dark:data-[state=active]:text-foreground data-[state=active]:bg-background dark:data-[state=active]:bg-background/90 transition-all duration-200 rounded-md"
              >
                <Wand2 className="h-4 w-4" />
                Build from Scratch
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="extract" className="space-y-4 mt-8">
            <Card className="border-border/50 bg-background/50">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5 text-primary" />
                  <CardTitle className="text-foreground">AI-Powered Extraction</CardTitle>
                </div>
                <CardDescription className="text-muted-foreground">
                  Our AI analyzes your conversations and automatically creates workflows from the tools you've used
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 text-muted-foreground -translate-y-1/2" />
                  <Input
                    placeholder="Search your conversations..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                    disabled={loading}
                  />
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium text-foreground">
                      Recent Conversations
                    </h3>
                    <Badge className="text-xs bg-blue-100 text-blue-700 dark:bg-blue-600 dark:text-white border-0">
                      {loading ? '...' : `${filteredThreads.length} available`}
                    </Badge>
                  </div>

                  {loading ? (
                    <div className="flex items-center justify-center h-[400px] border border-border/50 rounded-lg bg-muted/10">
                      <div className="flex flex-col items-center gap-2">
                        <Loader2 className="h-6 w-6 animate-spin text-primary" />
                        <p className="text-sm text-muted-foreground">Loading conversations...</p>
                      </div>
                    </div>
                  ) : filteredThreads.length === 0 ? (
                    <div className="flex items-center justify-center h-[400px] border border-dashed border-border/50 rounded-lg bg-muted/10">
                      <div className="text-center">
                        <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <p className="text-sm text-muted-foreground">No conversations found</p>
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-4"
                          onClick={fetchThreads}
                        >
                          <ArrowRight className="h-4 w-4 mr-2" />
                          Refresh
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <ScrollArea className="h-[400px] border border-border/50 rounded-lg bg-card/50">
                      <div className="space-y-2 p-4">
                        {filteredThreads.map((thread) => (
                          <Card
                            key={thread.thread_id}
                            className={`cursor-pointer transition-all duration-200 hover:shadow-sm border-border/50 ${
                              selectedThread?.thread_id === thread.thread_id
                                ? 'ring-2 ring-primary/50 border-primary/50 bg-primary/5'
                                : 'hover:bg-muted/10'
                            }`}
                            onClick={() => setSelectedThread(thread)}
                          >
                            <CardHeader className="pb-2">
                              <div className="flex items-start justify-between">
                                <CardTitle className="text-sm line-clamp-2 text-foreground">
                                  {thread.title}
                                </CardTitle>
                                <Badge variant="outline" className="text-xs shrink-0 ml-2">
                                  <Calendar className="w-3 h-3 mr-1" />
                                  {formatDistanceToNow(new Date(thread.created_at), { addSuffix: true })}
                                </Badge>
                              </div>
                            </CardHeader>
                            <CardContent className="pt-0">
                              {thread.preview && (
                                <p className="text-xs text-muted-foreground line-clamp-2">
                                  {thread.preview}
                                </p>
                              )}
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </ScrollArea>
                  )}

                  <Button
                    onClick={extractWorkflow}
                    disabled={extracting || !selectedThread}
                    className="w-full h-11"
                    size="lg"
                  >
                    {extracting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Extracting workflow...
                      </>
                    ) : (
                      <>
                        <Sparkles className="mr-2 h-4 w-4" />
                        Extract Workflow
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="manual" className="space-y-4 mt-8">
            <Card className="border-border/50 bg-background/50">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-2">
                  <Wand2 className="h-5 w-5 text-primary" />
                  <CardTitle className="text-foreground">Custom Workflow</CardTitle>
                </div>
                <CardDescription className="text-muted-foreground">
                  Start with a blank canvas and design your workflow step by step
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">Workflow Name</label>
                  <Input
                    placeholder="Give your workflow a descriptive name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">Description</label>
                  <Textarea
                    placeholder="Describe what this workflow will accomplish"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    rows={4}
                    className="resize-none"
                  />
                </div>
                <Button
                  onClick={createWorkflow}
                  disabled={loading || !name.trim()}
                  className="w-full h-11"
                  size="lg"
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating workflow...
                    </>
                  ) : (
                    <>
                      <Plus className="mr-2 h-4 w-4" />
                      Create Workflow
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
