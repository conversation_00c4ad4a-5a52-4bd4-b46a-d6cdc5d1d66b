'use client';

import { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { ArrowLeft, Play, Edit, Trash2, Calendar, Clock, FileText, Zap, Settings, CheckCircle, XCircle, Loader2, Network, Plus } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { createClient } from '@/lib/supabase/client';
import { toast } from 'sonner';
import { formatDistanceToNow, format } from 'date-fns';
import { WorkerHeader } from '@/components/workers/header';
import { EditStepModal } from '@/components/workflows/edit-step-modal';
import { EditWorkflowModal } from '@/components/workflows/edit-workflow-modal';
import { WorkflowEditor } from '@/components/workflows/workflow-editor';

interface WorkflowStep {
  step_id: string;
  step_order: number;
  tool_name: string;
  tool_parameters: Record<string, any>;
  explanation_text?: string;
  user_context?: string;
  parameter_config?: Record<string, any>;
}

interface Workflow {
  workflow_id: string;
  name: string;
  description: string;
  status: string;
  execution_count: number;
  source_thread_id?: string;
  steps: WorkflowStep[];
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
}

interface ExecutionLog {
  execution_id: string;
  workflow_id: string;
  status: string;
  started_at: string;
  completed_at?: string;
  error_message?: string;
  step_results: Record<string, any>;
}

export default function WorkflowDetailPage() {
  const params = useParams();
  const router = useRouter();
  const workflowId = params.workflowId as string;

  const [workflow, setWorkflow] = useState<Workflow | null>(null);
  const [executions, setExecutions] = useState<ExecutionLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [executing, setExecuting] = useState(false);
  const [editingStep, setEditingStep] = useState<WorkflowStep | null>(null);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editWorkflowModalOpen, setEditWorkflowModalOpen] = useState(false);

  const fetchWorkflow = async () => {
    try {
      setLoading(true);
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.access_token) {
        toast.error('Please log in to view workflow');
        return;
      }

      const response = await fetch(`/api/workflows/${workflowId}`, {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          toast.error('Workflow not found');
          router.push('/workflows');
          return;
        }
        throw new Error('Failed to fetch workflow');
      }

      const data = await response.json();
      setWorkflow(data.workflow);
    } catch (error) {
      console.error('Error fetching workflow:', error);
      toast.error('Failed to load workflow');
    } finally {
      setLoading(false);
    }
  };

  const executeWorkflow = async () => {
    try {
      setExecuting(true);
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.access_token) {
        toast.error('Please log in to execute workflow');
        return;
      }

      const response = await fetch(`/api/workflows/${workflowId}/execute`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}),
      });

      if (!response.ok) {
        throw new Error('Failed to execute workflow');
      }

      const data = await response.json();
      toast.success(data.message || 'Workflow execution started');

      // Refresh workflow data to update execution count
      fetchWorkflow();
    } catch (error) {
      console.error('Error executing workflow:', error);
      toast.error('Failed to execute workflow');
    } finally {
      setExecuting(false);
    }
  };

  const deleteWorkflow = async () => {
    try {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.access_token) {
        toast.error('Please log in to delete workflow');
        return;
      }

      const response = await fetch(`/api/workflows/${workflowId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to delete workflow');
      }

      toast.success('Workflow deleted successfully');
      router.push('/workflows');
    } catch (error) {
      console.error('Error deleting workflow:', error);
      toast.error('Failed to delete workflow');
    }
  };

  const handleEditStep = (step: WorkflowStep) => {
    setEditingStep(step);
    setEditModalOpen(true);
  };

  const handleSaveStep = async (updatedStep: WorkflowStep) => {
    try {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.access_token) {
        toast.error('Please log in to update workflow step');
        return;
      }

      const response = await fetch(`/api/workflows/${workflowId}/steps/${updatedStep.step_id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          explanation_text: updatedStep.explanation_text,
          user_context: updatedStep.user_context,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update workflow step');
      }

      toast.success('Step updated successfully');

      // Refresh workflow data
      fetchWorkflow();
    } catch (error) {
      console.error('Error updating step:', error);
      toast.error('Failed to update step');
      throw error;
    }
  };

  const handleSaveWorkflow = async (workflowData: Partial<Workflow>) => {
    try {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.access_token) {
        toast.error('Please log in to update workflow');
        return;
      }

      // Always send the full workflow object
      const payload = {
        name: workflowData.name ?? workflow?.name,
        description: workflowData.description ?? workflow?.description,
        steps: workflow?.steps ?? [],
        metadata: { ...workflow?.metadata, status: workflowData.status },
      };

      const response = await fetch(`/api/workflows/${workflowId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error('Failed to update workflow');
      }

      toast.success('Workflow updated successfully');
      fetchWorkflow();
    } catch (error) {
      console.error('Error updating workflow:', error);
      toast.error('Failed to update workflow');
      throw error;
    }
  };

  const handleAddStep = async (stepData: Omit<WorkflowStep, 'step_id'>) => {
    try {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.access_token) {
        toast.error('Please log in to add workflow step');
        return;
      }

      const response = await fetch(`/api/workflows/${workflowId}/steps`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(stepData),
      });

      if (!response.ok) {
        throw new Error('Failed to add workflow step');
      }

      toast.success('Step added successfully');
      fetchWorkflow();
    } catch (error) {
      console.error('Error adding step:', error);
      toast.error('Failed to add step');
      throw error;
    }
  };

  const handleDeleteStep = async (stepId: string) => {
    try {
      const supabase = createClient();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.access_token) {
        toast.error('Please log in to delete workflow step');
        return;
      }

      const response = await fetch(`/api/workflows/${workflowId}/steps/${stepId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to delete workflow step');
      }

      toast.success('Step deleted successfully');
      fetchWorkflow();
    } catch (error) {
      console.error('Error deleting step:', error);
      toast.error('Failed to delete step');
      throw error;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950/50 dark:text-green-400 dark:border-green-800';
      case 'archived': return 'bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-950/50 dark:text-gray-400 dark:border-gray-800';
      case 'template': return 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950/50 dark:text-blue-400 dark:border-blue-800';
      default: return 'bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-950/50 dark:text-gray-400 dark:border-gray-800';
    }
  };

  const getToolIcon = (toolName: string) => {
    switch (toolName.toLowerCase()) {
      case 'edit_file':
      case 'create_file':
        return <FileText className="h-4 w-4" />;
      case 'run_terminal_cmd':
      case 'execute_command':
        return <Zap className="h-4 w-4" />;
      default:
        return <Settings className="h-4 w-4" />;
    }
  };

  useEffect(() => {
    if (workflowId) {
      fetchWorkflow();
    }
  }, [workflowId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!workflow) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] p-8">
        <XCircle className="h-12 w-12 text-muted-foreground mb-4" />
        <h2 className="text-xl font-semibold mb-2">Workflow not found</h2>
        <p className="text-muted-foreground mb-4">
          The workflow you're looking for doesn't exist or has been deleted.
        </p>
        <Button onClick={() => router.push('/workflows')}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Workflows
        </Button>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <WorkerHeader
        title={workflow.name}
        description={workflow.description}
        icon={<FileText className="h-7 w-7 text-foreground/70" />}
        action={
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              onClick={() => setEditWorkflowModalOpen(true)}
            >
              <Edit className="mr-2 h-4 w-4" />
              Edit Workflow
            </Button>
            <Button
              variant="outline"
              onClick={() => router.push('/workflows')}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            <Button
              onClick={executeWorkflow}
              disabled={executing}
              className="bg-primary hover:bg-primary/90"
            >
              {executing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Executing...
                </>
              ) : (
                <>
                  <Play className="mr-2 h-4 w-4" />
                  Execute Workflow
                </>
              )}
            </Button>
          </div>
        }
      />

      <div className="flex-1 p-4 md:p-6 space-y-6">
        {/* Workflow Overview */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card className="bg-background border-border/50">
            <CardHeader className="pb-2">
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className={`${getStatusColor(workflow.status)} text-xs font-medium`}>
                  {workflow.status}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{workflow.execution_count}</div>
              <p className="text-xs text-muted-foreground">Total Executions</p>
            </CardContent>
          </Card>

          <Card className="bg-background border-border/50">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Steps</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{workflow.steps?.length || 0}</div>
              <p className="text-xs text-muted-foreground">Total Steps</p>
            </CardContent>
          </Card>

          <Card className="bg-background border-border/50">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Created</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm font-medium">{format(new Date(workflow.created_at), 'MMM dd, yyyy')}</div>
              <p className="text-xs text-muted-foreground">{formatDistanceToNow(new Date(workflow.created_at))} ago</p>
            </CardContent>
          </Card>
        </div>

        {/* Unified Workflow Editor */}
        <WorkflowEditor
          steps={workflow.steps || []}
          onUpdateStep={handleSaveStep}
          onAddStep={handleAddStep}
          onDeleteStep={handleDeleteStep}
          onEditStep={handleEditStep}
        />

        {/* Danger Zone */}
        <Card className="bg-background border-destructive/20">
          <CardHeader>
            <CardTitle className="text-destructive">Danger Zone</CardTitle>
            <CardDescription>
              Irreversible and destructive actions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium">Delete Workflow</h4>
                <p className="text-sm text-muted-foreground">
                  Once you delete a workflow, there is no going back. Please be certain.
                </p>
              </div>
              <Button
                variant="destructive"
                onClick={deleteWorkflow}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Modals */}
      <EditStepModal
        step={editingStep}
        open={editModalOpen}
        onOpenChange={setEditModalOpen}
        onSave={handleSaveStep}
      />

      <EditWorkflowModal
        workflow={workflow}
        open={editWorkflowModalOpen}
        onOpenChange={setEditWorkflowModalOpen}
        onSave={handleSaveWorkflow}
      />
    </div>
  );
}
