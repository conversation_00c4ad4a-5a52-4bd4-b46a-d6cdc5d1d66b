import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Fetch tools from the backend
    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;
    if (!backendUrl) {
      throw new Error('NEXT_PUBLIC_BACKEND_URL environment variable is not set');
    }

    console.log('Fetching tools from backend:', `${backendUrl}/tools`);
    const response = await fetch(`${backendUrl}/tools`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend response error:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });
      throw new Error(`Failed to fetch tools from backend: ${response.status} ${response.statusText}`);
    }

    const tools = await response.json();
    console.log('Received tools from backend:', tools);

    if (!Array.isArray(tools)) {
      console.error('Invalid tools data format:', tools);
      throw new Error('Invalid tools data format received from backend');
    }

    // Transform the tools data to match our frontend interface
    const transformedTools = tools.map((tool: any) => {
      if (!tool || typeof tool !== 'object') {
        console.warn('Invalid tool object:', tool);
        return null;
      }

      return {
        id: tool.name || 'unknown',
        name: tool.name || 'unknown',
        description: tool.description || 'No description available',
        type: tool.type || 'function',
        parameters: tool.parameters || {},
        created_at: new Date().toISOString(),
      };
    }).filter(Boolean); // Remove any null entries

    console.log('Transformed tools:', transformedTools);
    return NextResponse.json(transformedTools);
  } catch (error) {
    console.error('Error in /api/tools route:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch tools' },
      { status: 500 }
    );
  }
}
