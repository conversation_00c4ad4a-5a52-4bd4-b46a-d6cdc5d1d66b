import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { workflowId: string } }
) {
  try {
    // Get the authorization header from the request
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization header required' }, { status: 401 });
    }

    // Fetch specific workflow from the backend
    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;
    if (!backendUrl) {
      throw new Error('NEXT_PUBLIC_BACKEND_URL environment variable is not set');
    }

    console.log('Fetching workflow from backend:', `${backendUrl}/workflows/${params.workflowId}`);
    const response = await fetch(`${backendUrl}/workflows/${params.workflowId}`, {
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend response error:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });
      throw new Error(`Failed to fetch workflow from backend: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('Received workflow from backend:', data);

    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error in GET /api/workflows/${params.workflowId} route:`, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch workflow' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { workflowId: string } }
) {
  try {
    // Get the authorization header from the request
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization header required' }, { status: 401 });
    }

    // Get the request body
    const body = await request.json();

    // Update workflow on the backend
    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;
    if (!backendUrl) {
      throw new Error('NEXT_PUBLIC_BACKEND_URL environment variable is not set');
    }

    console.log('Updating workflow on backend:', `${backendUrl}/workflows/${params.workflowId}`);
    const response = await fetch(`${backendUrl}/workflows/${params.workflowId}`, {
      method: 'PUT',
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend response error:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });
      throw new Error(`Failed to update workflow on backend: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('Updated workflow on backend:', data);

    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error in PUT /api/workflows/${params.workflowId} route:`, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update workflow' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { workflowId: string } }
) {
  try {
    // Get the authorization header from the request
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization header required' }, { status: 401 });
    }

    // Delete workflow on the backend
    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;
    if (!backendUrl) {
      throw new Error('NEXT_PUBLIC_BACKEND_URL environment variable is not set');
    }

    console.log('Deleting workflow on backend:', `${backendUrl}/workflows/${params.workflowId}`);
    const response = await fetch(`${backendUrl}/workflows/${params.workflowId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend response error:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });
      throw new Error(`Failed to delete workflow on backend: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('Deleted workflow on backend:', data);

    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error in DELETE /api/workflows/${params.workflowId} route:`, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to delete workflow' },
      { status: 500 }
    );
  }
}
