import { NextRequest, NextResponse } from 'next/server';

export async function POST(
  request: NextRequest,
  { params }: { params: { workflowId: string } }
) {
  try {
    // Get the authorization header from the request
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization header required' }, { status: 401 });
    }

    // Get the request body
    const body = await request.json();

    // Add workflow step on the backend
    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;
    if (!backendUrl) {
      throw new Error('NEXT_PUBLIC_BACKEND_URL environment variable is not set');
    }

    console.log('Adding workflow step on backend:', `${backendUrl}/workflows/${params.workflowId}/steps`);
    const response = await fetch(`${backendUrl}/workflows/${params.workflowId}/steps`, {
      method: 'POST',
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend response error:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });
      throw new Error(`Failed to add workflow step on backend: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('Added workflow step on backend:', data);

    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error in POST /api/workflows/${params.workflowId}/steps route:`, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to add workflow step' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { workflowId: string } }
) {
  try {
    // Get the authorization header from the request
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization header required' }, { status: 401 });
    }

    // Get the request body
    const body = await request.json();

    // Reorder workflow steps on the backend
    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;
    if (!backendUrl) {
      throw new Error('NEXT_PUBLIC_BACKEND_URL environment variable is not set');
    }

    console.log('Reordering workflow steps on backend:', `${backendUrl}/workflows/${params.workflowId}/steps/reorder`);
    const response = await fetch(`${backendUrl}/workflows/${params.workflowId}/steps/reorder`, {
      method: 'PUT',
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend response error:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });
      throw new Error(`Failed to reorder workflow steps on backend: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('Reordered workflow steps on backend:', data);

    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error in PUT /api/workflows/${params.workflowId}/steps route:`, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to reorder workflow steps' },
      { status: 500 }
    );
  }
}
