import { NextRequest, NextResponse } from 'next/server';

export async function PUT(
  request: NextRequest,
  { params }: { params: { workflowId: string; stepId: string } }
) {
  try {
    // Get the authorization header from the request
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization header required' }, { status: 401 });
    }

    // Get the request body
    const body = await request.json();

    // Update workflow step on the backend
    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;
    if (!backendUrl) {
      throw new Error('NEXT_PUBLIC_BACKEND_URL environment variable is not set');
    }

    console.log('Updating workflow step on backend:', `${backendUrl}/workflows/${params.workflowId}/steps/${params.stepId}`);
    const response = await fetch(`${backendUrl}/workflows/${params.workflowId}/steps/${params.stepId}`, {
      method: 'PUT',
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend response error:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });
      throw new Error(`Failed to update workflow step on backend: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('Updated workflow step on backend:', data);

    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error in PUT /api/workflows/${params.workflowId}/steps/${params.stepId} route:`, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update workflow step' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { workflowId: string; stepId: string } }
) {
  try {
    // Get the authorization header from the request
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization header required' }, { status: 401 });
    }

    // Delete workflow step on the backend
    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;
    if (!backendUrl) {
      throw new Error('NEXT_PUBLIC_BACKEND_URL environment variable is not set');
    }

    console.log('Deleting workflow step on backend:', `${backendUrl}/workflows/${params.workflowId}/steps/${params.stepId}`);
    const response = await fetch(`${backendUrl}/workflows/${params.workflowId}/steps/${params.stepId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend response error:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });
      throw new Error(`Failed to delete workflow step on backend: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('Deleted workflow step on backend:', data);

    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error in DELETE /api/workflows/${params.workflowId}/steps/${params.stepId} route:`, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to delete workflow step' },
      { status: 500 }
    );
  }
}
