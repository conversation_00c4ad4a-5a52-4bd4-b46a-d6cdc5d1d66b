import { NextRequest, NextResponse } from 'next/server';

export async function POST(
  request: NextRequest,
  { params }: { params: { workflowId: string } }
) {
  try {
    // Get the authorization header from the request
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization header required' }, { status: 401 });
    }

    // Get the request body
    const body = await request.json();

    // Execute workflow on the backend
    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;
    if (!backendUrl) {
      throw new Error('NEXT_PUBLIC_BACKEND_URL environment variable is not set');
    }

    console.log('Executing workflow on backend:', `${backendUrl}/workflows/${params.workflowId}/execute`);
    const response = await fetch(`${backendUrl}/workflows/${params.workflowId}/execute`, {
      method: 'POST',
      headers: {
        'Authorization': authHeader,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend response error:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });
      throw new Error(`Failed to execute workflow on backend: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('Executed workflow on backend:', data);

    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error in POST /api/workflows/${params.workflowId}/execute route:`, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to execute workflow' },
      { status: 500 }
    );
  }
}
