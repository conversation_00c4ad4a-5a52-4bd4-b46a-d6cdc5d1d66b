import { Metadata } from 'next';
import { siteConfig } from '@/lib/site';

export const metadata: Metadata = {
  title: 'Atlas',
  description: 'Your AI Assistant',
  keywords: ['Atlas', 'AI', 'Agent'],
  authors: [
    {
      name: 'Atlas Agents',
      url: 'https://atlasagents.ai',
    },
  ],
  creator: 'Atlas Agents',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://atlasagents.ai',
    title: siteConfig.name,
    description: siteConfig.description,
    siteName: siteConfig.name,
    images: ['https://atlasagents.ai/preview.png'],
  },
  twitter: {
    card: 'summary_large_image',
    title: siteConfig.name,
    description: siteConfig.description,
    creator: '@kortixai',
    images: ['https://atlasagents.ai/preview.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};
