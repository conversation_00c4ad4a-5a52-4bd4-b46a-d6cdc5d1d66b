"""
Workflow extraction API endpoints.

This module provides REST API endpoints for extracting workflows from chat messages,
managing workflows, and executing workflows.
"""

from fastapi import APIRouter, HTTPException, Depends, Body
from fastapi.responses import J<PERSON>NResponse
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from datetime import datetime
import uuid
import json

from services.workflow_extraction import WorkflowExtractionService
from services.supabase import DBConnection
from utils.auth_utils import get_current_user_id_from_jwt, get_account_id_from_thread
from utils.logger import logger


router = APIRouter()
db = DBConnection()
workflow_service = WorkflowExtractionService()


# Pydantic models for request/response
class ExtractWorkflowsRequest(BaseModel):
    thread_id: str
    model_name: Optional[str] = "gpt-4o-mini"


class WorkflowResponse(BaseModel):
    workflow_id: str
    name: str
    description: str
    status: str
    execution_count: int
    source_thread_id: Optional[str]
    steps: List[Dict[str, Any]]
    metadata: Dict[str, Any]
    created_at: str
    updated_at: str


class CreateWorkflowRequest(BaseModel):
    name: str
    description: str
    steps: List[Dict[str, Any]]
    metadata: Optional[Dict[str, Any]] = {}


class ExecuteWorkflowRequest(BaseModel):
    workflow_id: str
    parameters: Optional[Dict[str, Any]] = {}


async def get_user_account_id(user_id: str) -> str:
    """
    Get the user's primary account ID using basejump patterns.
    For personal accounts, the account_id is the same as user_id.
    """
    try:
        client = await db.client

        # Query basejump.account_user to get the user's account
        # For personal accounts, we can use user_id directly as account_id
        account_result = await client.rpc("get_accounts").execute()

        if not account_result.data:
            # Fallback: for personal accounts, account_id equals user_id
            return user_id

        # Parse the JSON response from get_accounts function
        accounts = (
            json.loads(account_result.data)
            if isinstance(account_result.data, str)
            else account_result.data
        )

        if accounts and len(accounts) > 0:
            # Return the first account (typically the personal account)
            return accounts[0]["account_id"]

        # Fallback: use user_id as account_id for personal accounts
        return user_id

    except Exception as e:
        logger.error(f"Error getting account_id for user {user_id}: {e}")
        # Fallback: use user_id as account_id for personal accounts
        return user_id


@router.post("/workflows/extract")
async def extract_workflows_from_thread(
    request: ExtractWorkflowsRequest = Body(...),
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    Extract workflows from a specific thread's messages.

    This endpoint analyzes assistant messages in a thread that contain XML tool calls
    and uses an AI agent to extract reusable workflows from them.
    """
    try:
        logger.info(
            f"Extracting workflows from thread {request.thread_id} for user {user_id}"
        )

        # Get account ID from the thread (with permission verification)
        client = await db.client
        account_id = await get_account_id_from_thread(client, request.thread_id)

        # Extract workflows
        workflows = await workflow_service.extract_workflows_from_thread(
            thread_id=request.thread_id,
            account_id=account_id,
            model_name=request.model_name,
        )

        if not workflows:
            return JSONResponse(
                status_code=200,
                content={
                    "message": "No workflows found in the specified thread",
                    "workflows": [],
                },
            )

        # Save extracted workflows
        saved_workflows = []
        for workflow_data in workflows:
            try:
                workflow_id = await workflow_service.save_workflow(workflow_data)
                workflow_data["workflow_id"] = workflow_id
                saved_workflows.append(workflow_data)
                logger.info(f"Saved workflow {workflow_id}: {workflow_data['name']}")
            except Exception as e:
                logger.error(f"Failed to save workflow '{workflow_data['name']}': {e}")
                continue

        return JSONResponse(
            status_code=200,
            content={
                "message": f"Successfully extracted and saved {len(saved_workflows)} workflows",
                "workflows": saved_workflows,
            },
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error extracting workflows from thread {request.thread_id}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to extract workflows: {str(e)}"
        )


@router.get("/workflows")
async def get_user_workflows(user_id: str = Depends(get_current_user_id_from_jwt)):
    """
    Get all workflows for the authenticated user.
    """
    try:
        logger.info(f"Getting workflows for user {user_id}")

        # Get user's account ID
        account_id = await get_user_account_id(user_id)

        # Get workflows
        workflows = await workflow_service.get_user_workflows(account_id)

        return JSONResponse(status_code=200, content={"workflows": workflows})

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting workflows for user {user_id}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get workflows: {str(e)}"
        )


@router.get("/workflows/{workflow_id}")
async def get_workflow(
    workflow_id: str, user_id: str = Depends(get_current_user_id_from_jwt)
):
    """
    Get a specific workflow by ID.
    """
    try:
        logger.info(f"Getting workflow {workflow_id} for user {user_id}")

        # Get user's account ID
        account_id = await get_user_account_id(user_id)

        # Get specific workflow with access check
        client = await db.client
        workflow_result = (
            await client.table("workflows")
            .select("*, workflow_steps(*)")
            .eq("workflow_id", workflow_id)
            .eq("account_id", account_id)
            .execute()
        )

        if not workflow_result.data:
            raise HTTPException(status_code=404, detail="Workflow not found")

        workflow = workflow_result.data[0]

        # Sort steps by order
        steps = sorted(
            workflow.get("workflow_steps", []), key=lambda x: x["step_order"]
        )
        workflow["steps"] = steps
        del workflow["workflow_steps"]  # Clean up the nested structure

        return JSONResponse(status_code=200, content={"workflow": workflow})

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting workflow {workflow_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get workflow: {str(e)}")


@router.post("/workflows")
async def create_workflow(
    request: CreateWorkflowRequest = Body(...),
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    Create a new workflow manually.
    """
    try:
        logger.info(f"Creating workflow '{request.name}' for user {user_id}")

        # Get user's account ID
        account_id = await get_user_account_id(user_id)

        # Prepare workflow data
        workflow_data = {
            "name": request.name,
            "description": request.description,
            "account_id": account_id,
            "steps": request.steps,
            "metadata": {
                **request.metadata,
                "created_manually": True,
                "created_at": datetime.utcnow().isoformat(),
            },
        }

        # Save workflow
        workflow_id = await workflow_service.save_workflow(workflow_data)

        return JSONResponse(
            status_code=201,
            content={
                "message": "Workflow created successfully",
                "workflow_id": workflow_id,
            },
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating workflow for user {user_id}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to create workflow: {str(e)}"
        )


@router.put("/workflows/{workflow_id}")
async def update_workflow(
    workflow_id: str,
    request: CreateWorkflowRequest = Body(...),
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    Update an existing workflow.
    """
    try:
        logger.info(f"Updating workflow {workflow_id} for user {user_id}")

        # Get user's account ID
        account_id = await get_user_account_id(user_id)

        # Check if workflow exists and user owns it
        client = await db.client
        workflow_result = (
            await client.table("workflows")
            .select("workflow_id")
            .eq("workflow_id", workflow_id)
            .eq("account_id", account_id)
            .execute()
        )

        if not workflow_result.data:
            raise HTTPException(status_code=404, detail="Workflow not found")

        # Update workflow
        update_result = (
            await client.table("workflows")
            .update(
                {
                    "name": request.name,
                    "description": request.description,
                    "metadata": request.metadata,
                }
            )
            .eq("workflow_id", workflow_id)
            .execute()
        )

        # Delete existing steps
        await client.table("workflow_steps").delete().eq(
            "workflow_id", workflow_id
        ).execute()

        # Create new steps
        steps_data = []
        for i, step in enumerate(request.steps):
            steps_data.append(
                {
                    "workflow_id": workflow_id,
                    "step_order": i + 1,
                    "tool_name": step["tool_name"],
                    "tool_parameters": step["tool_parameters"],
                    "explanation_text": step.get("explanation_text"),
                    "user_context": step.get("user_context"),
                    "parameter_config": step.get("parameter_config", {}),
                }
            )

        if steps_data:
            await client.table("workflow_steps").insert(steps_data).execute()

        return JSONResponse(
            status_code=200, content={"message": "Workflow updated successfully"}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating workflow {workflow_id}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to update workflow: {str(e)}"
        )


@router.delete("/workflows/{workflow_id}")
async def delete_workflow(
    workflow_id: str, user_id: str = Depends(get_current_user_id_from_jwt)
):
    """
    Delete a workflow.
    """
    try:
        logger.info(f"Deleting workflow {workflow_id} for user {user_id}")

        # Get user's account ID
        account_id = await get_user_account_id(user_id)

        # Delete workflow (verifies ownership)
        success = await workflow_service.delete_workflow(workflow_id, account_id)

        if not success:
            raise HTTPException(status_code=404, detail="Workflow not found")

        return JSONResponse(
            status_code=200, content={"message": "Workflow deleted successfully"}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting workflow {workflow_id}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to delete workflow: {str(e)}"
        )


@router.post("/workflows/{workflow_id}/steps")
async def add_workflow_step(
    workflow_id: str,
    request: dict = Body(...),
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    Add a new step to a workflow.
    """
    try:
        logger.info(f"Adding step to workflow {workflow_id} for user {user_id}")

        # Get user's account ID
        account_id = await get_user_account_id(user_id)

        # Verify workflow exists and user owns it
        client = await db.client
        workflow_result = (
            await client.table("workflows")
            .select("workflow_id")
            .eq("workflow_id", workflow_id)
            .eq("account_id", account_id)
            .execute()
        )

        if not workflow_result.data:
            raise HTTPException(status_code=404, detail="Workflow not found")

        # Create the new step
        step_data = {
            "workflow_id": workflow_id,
            "step_order": request.get("step_order", 1),
            "tool_name": request["tool_name"],
            "tool_parameters": request.get("tool_parameters", {}),
            "explanation_text": request.get("explanation_text"),
            "user_context": request.get("user_context"),
            "parameter_config": request.get("parameter_config", {}),
        }

        step_result = await client.table("workflow_steps").insert(step_data).execute()

        if not step_result.data:
            raise HTTPException(
                status_code=500, detail="Failed to create workflow step"
            )

        return JSONResponse(
            status_code=201,
            content={
                "message": "Workflow step added successfully",
                "step": step_result.data[0],
            },
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding step to workflow {workflow_id}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to add workflow step: {str(e)}"
        )


@router.delete("/workflows/{workflow_id}/steps/{step_id}")
async def delete_workflow_step(
    workflow_id: str,
    step_id: str,
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    Delete a specific workflow step.
    """
    try:
        logger.info(
            f"Deleting step {step_id} from workflow {workflow_id} for user {user_id}"
        )

        # Get user's account ID
        account_id = await get_user_account_id(user_id)

        # Verify workflow exists and user owns it
        client = await db.client
        workflow_result = (
            await client.table("workflows")
            .select("workflow_id")
            .eq("workflow_id", workflow_id)
            .eq("account_id", account_id)
            .execute()
        )

        if not workflow_result.data:
            raise HTTPException(status_code=404, detail="Workflow not found")

        # Delete the step
        step_result = (
            await client.table("workflow_steps")
            .delete()
            .eq("step_id", step_id)
            .eq("workflow_id", workflow_id)
            .execute()
        )

        if not step_result.data:
            raise HTTPException(status_code=404, detail="Workflow step not found")

        return JSONResponse(
            status_code=200, content={"message": "Workflow step deleted successfully"}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting workflow step {step_id}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to delete workflow step: {str(e)}"
        )


@router.put("/workflows/{workflow_id}/steps/reorder")
async def reorder_workflow_steps(
    workflow_id: str,
    request: dict = Body(...),
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    Reorder workflow steps.
    """
    try:
        logger.info(f"Reordering steps for workflow {workflow_id} for user {user_id}")

        # Get user's account ID
        account_id = await get_user_account_id(user_id)

        # Verify workflow exists and user owns it
        client = await db.client
        workflow_result = (
            await client.table("workflows")
            .select("workflow_id")
            .eq("workflow_id", workflow_id)
            .eq("account_id", account_id)
            .execute()
        )

        if not workflow_result.data:
            raise HTTPException(status_code=404, detail="Workflow not found")

        # Get the updated steps order
        steps = request.get("steps", [])
        if not steps:
            raise HTTPException(status_code=400, detail="No steps provided")

        # Update each step's order
        for step in steps:
            await client.table("workflow_steps").update(
                {"step_order": step["step_order"]}
            ).eq("step_id", step["step_id"]).eq("workflow_id", workflow_id).execute()

        return JSONResponse(
            status_code=200,
            content={"message": "Workflow steps reordered successfully"},
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reordering workflow steps {workflow_id}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to reorder workflow steps: {str(e)}"
        )


@router.put("/workflows/{workflow_id}/steps/{step_id}")
async def update_workflow_step(
    workflow_id: str,
    step_id: str,
    request: dict = Body(...),
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    Update a specific workflow step.
    """
    try:
        logger.info(
            f"Updating step {step_id} in workflow {workflow_id} for user {user_id}"
        )

        # Get user's account ID
        account_id = await get_user_account_id(user_id)

        # Verify workflow exists and user owns it
        client = await db.client
        workflow_result = (
            await client.table("workflows")
            .select("workflow_id")
            .eq("workflow_id", workflow_id)
            .eq("account_id", account_id)
            .execute()
        )

        if not workflow_result.data:
            raise HTTPException(status_code=404, detail="Workflow not found")

        # Update the specific step
        update_data = {}
        if "explanation_text" in request:
            update_data["explanation_text"] = request["explanation_text"]
        if "user_context" in request:
            update_data["user_context"] = request["user_context"]

        if not update_data:
            raise HTTPException(status_code=400, detail="No valid fields to update")

        step_result = (
            await client.table("workflow_steps")
            .update(update_data)
            .eq("step_id", step_id)
            .eq("workflow_id", workflow_id)
            .execute()
        )

        if not step_result.data:
            raise HTTPException(status_code=404, detail="Workflow step not found")

        return JSONResponse(
            status_code=200,
            content={
                "message": "Workflow step updated successfully",
                "step": step_result.data[0],
            },
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating workflow step {step_id}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to update workflow step: {str(e)}"
        )


@router.post("/workflows/{workflow_id}/execute")
async def execute_workflow(
    workflow_id: str,
    request: ExecuteWorkflowRequest = Body(default={}),
    user_id: str = Depends(get_current_user_id_from_jwt),
):
    """
    Execute a workflow in a new thread.

    Note: This is a placeholder for future implementation.
    The actual execution would involve creating a new thread and
    running the workflow steps through the main Atlas agent.
    """
    try:
        logger.info(f"Execute workflow {workflow_id} requested for user {user_id}")

        # Get user's account ID
        account_id = await get_user_account_id(user_id)

        # Verify workflow exists
        client = await db.client
        workflow_result = (
            await client.table("workflows")
            .select("name, description")
            .eq("workflow_id", workflow_id)
            .eq("account_id", account_id)
            .execute()
        )

        if not workflow_result.data:
            raise HTTPException(status_code=404, detail="Workflow not found")

        # For now, return a placeholder response
        # TODO: Implement actual workflow execution
        return JSONResponse(
            status_code=200,
            content={
                "message": "Workflow execution is not yet implemented",
                "workflow_id": workflow_id,
                "status": "pending_implementation",
            },
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error executing workflow {workflow_id}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to execute workflow: {str(e)}"
        )
