# Composio OpenAI Integration Guide

This document explains the fully functional Composio integration with OpenAI tools alongside existing XML tools in the Atlas agent system.

## Overview

The integration allows users to use Composio tools (Gmail, Notion, GitHub, Slack, etc.) through the Atlas agent on a per-user basis. Each user's credentials are securely stored in Supabase and dynamically fetched during tool execution.

## Architecture

### Key Components

1. **ComposioOpenAIService** (`backend/services/composio_openai_service.py`)
   - Manages per-user Composio tool integration
   - Fetches user credentials from Supabase
   - Handles tool registration and execution

2. **ToolRegistry** (`backend/agentpress/tool_registry.py`)
   - Registers both XML and OpenAI tools
   - Marks Composio tools for special handling
   - Delegates Composio tool execution to ComposioOpenAIService

3. **ThreadManager** (`backend/agentpress/thread_manager.py`)
   - Accepts user_id for Composio tool context
   - Passes user context to ResponseProcessor

4. **ResponseProcessor** (`backend/agentpress/response_processor.py`)
   - Executes tools with user context
   - Handles both XML and OpenAI tool calls

## Database Schema

The integration uses the existing `user_mcp_connections` table:

```sql
-- Stores user Composio credentials and service connections
CREATE TABLE user_mcp_connections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id TEXT NOT NULL,
    service_name TEXT NOT NULL,
    composio_entity_id TEXT,
    status TEXT DEFAULT 'pending',
    is_active BOOLEAN DEFAULT true,
    credentials JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## How It Works

### 1. User Authentication
- Users authenticate with Composio services via the existing auth flow
- Credentials are stored in `user_mcp_connections` table with `composio_entity_id`

### 2. Tool Registration
```python
# In agent/run.py
if user_id:
    # Get user's active services
    active_services = composio_service.get_user_active_services_sync(user_id)
    
    # Get OpenAI tools for active services
    openai_tools = composio_openai_service.get_tools(user_id, active_services)
    
    # Register tools with Composio marking
    thread_manager.add_openai_tools(openai_tools, mark_as_composio=True)
```

### 3. Tool Execution
```python
# When a Composio tool is called
if tool.get("composio_tool", False):
    # Use ComposioOpenAIService for execution
    composio_service = ComposioOpenAIService.from_env()
    results = composio_service.handle_tool_calls(user_id, [tool_call])
```

## Key Features

### ✅ Per-User Credentials
- Each user has their own Composio entity ID
- Credentials are securely stored and retrieved from Supabase
- No hardcoded entity IDs

### ✅ Dynamic Service Discovery
- Automatically detects user's active services
- Only loads tools for connected services
- Caches tool definitions for performance

### ✅ Dual Tool Support
- XML tools and OpenAI tools work side by side
- Composio tools are marked for special handling
- Existing XML tools remain unchanged

### ✅ Error Handling
- Graceful fallback when no credentials found
- Detailed logging for debugging
- Non-blocking errors (agent continues without Composio tools)

## Usage Examples

### Basic Usage
```python
# Initialize ThreadManager with user context
thread_manager = ThreadManager(user_id="user_123")

# Tools are automatically loaded based on user's active services
# No additional configuration needed
```

### Manual Tool Loading
```python
# Get specific services for a user
composio_service = ComposioOpenAIService.from_env()
tools = composio_service.get_tools("user_123", ["gmail", "notion"])

# Register tools
thread_manager.add_openai_tools(tools, mark_as_composio=True)
```

### Tool Execution
```python
# Tools are executed automatically when called by the LLM
# The system handles routing to appropriate execution method
result = tool_registry.execute_openai_tool(
    name="gmail_send_email",
    arguments={"to": "<EMAIL>", "subject": "Test"},
    user_id="user_123"
)
```

## Configuration

### Environment Variables
```bash
# Required for Composio
COMPOSIO_API_KEY=your_composio_api_key

# Required for Supabase (credential storage)
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
```

### Service Mapping
The system maps service names to Composio App enums:

```python
service_mappings = {
    "gmail": App.GMAIL,
    "notion": App.NOTION,
    "github": App.GITHUB,
    "slack": App.SLACK,
    "google_drive": App.GOOGLEDRIVE,
    # Add more as needed
}
```

## Testing

Run the integration test:

```bash
cd backend
python test_composio_integration.py
```

This will test:
- Service initialization
- User credential retrieval
- Tool registration
- ThreadManager integration

## Troubleshooting

### No Tools Loaded
- Check if user has active services in `user_mcp_connections`
- Verify `composio_entity_id` is set for the user
- Check Composio API key configuration

### Tool Execution Fails
- Verify user has valid credentials for the service
- Check service-specific permissions
- Review logs for detailed error messages

### Performance Issues
- Tool definitions are cached per user/service combination
- Entity IDs are cached per user
- Consider implementing TTL for caches if needed

## Migration Notes

### From Previous Implementation
- Remove hardcoded entity IDs
- Update any direct Composio calls to use the new service
- Ensure user_id is passed through the call chain

### Backward Compatibility
- Existing XML tools continue to work unchanged
- Non-Composio OpenAI tools work as before
- System gracefully handles missing user context

## Future Enhancements

1. **Cache TTL**: Add time-based cache expiration
2. **Service Health**: Monitor service connection status
3. **Bulk Operations**: Optimize for multiple users
4. **Analytics**: Track tool usage per user/service
5. **Rate Limiting**: Implement per-user rate limits
