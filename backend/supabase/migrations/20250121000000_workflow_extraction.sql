-- WORKFLOW EXTRACTION SCHEMA:
-- Create workflows table
CREATE TABLE workflows (
    workflow_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    account_id UUID NOT NULL REFERENCES basejump.accounts(id) ON DELETE CASCADE,
    source_thread_id UUID REFERENCES threads(thread_id) ON DELETE SET NULL,
    status TEXT NOT NULL DEFAULT 'active', -- active, archived, template
    execution_count INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create workflow_steps table
CREATE TABLE workflow_steps (
    step_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_id UUID NOT NULL REFERENCES workflows(workflow_id) ON DELETE CASCADE,
    step_order INTEGER NOT NULL,
    tool_name TEXT NOT NULL,
    tool_parameters JSONB NOT NULL DEFAULT '{}'::jsonb,
    explanation_text TEXT,
    user_context TEXT,
    parameter_config JSONB DEFAULT '{}'::jsonb, -- defines which params are static/variable/dynamic
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    UNIQUE(workflow_id, step_order)
);

-- Create workflow_executions table for tracking runs
CREATE TABLE workflow_executions (
    execution_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_id UUID NOT NULL REFERENCES workflows(workflow_id) ON DELETE CASCADE,
    thread_id UUID REFERENCES threads(thread_id) ON DELETE SET NULL,
    status TEXT NOT NULL DEFAULT 'running', -- running, completed, failed, cancelled
    started_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    execution_metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create triggers for updated_at
CREATE TRIGGER update_workflows_updated_at
    BEFORE UPDATE ON workflows
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_workflow_steps_updated_at
    BEFORE UPDATE ON workflow_steps
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_workflow_executions_updated_at
    BEFORE UPDATE ON workflow_executions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for better query performance
CREATE INDEX idx_workflows_account_id ON workflows(account_id);
CREATE INDEX idx_workflows_source_thread_id ON workflows(source_thread_id);
CREATE INDEX idx_workflows_status ON workflows(status);
CREATE INDEX idx_workflows_created_at ON workflows(created_at);

CREATE INDEX idx_workflow_steps_workflow_id ON workflow_steps(workflow_id);
CREATE INDEX idx_workflow_steps_order ON workflow_steps(workflow_id, step_order);
CREATE INDEX idx_workflow_steps_tool_name ON workflow_steps(tool_name);

CREATE INDEX idx_workflow_executions_workflow_id ON workflow_executions(workflow_id);
CREATE INDEX idx_workflow_executions_thread_id ON workflow_executions(thread_id);
CREATE INDEX idx_workflow_executions_status ON workflow_executions(status);
CREATE INDEX idx_workflow_executions_created_at ON workflow_executions(created_at);

-- Enable Row Level Security
ALTER TABLE workflows ENABLE ROW LEVEL SECURITY;
ALTER TABLE workflow_steps ENABLE ROW LEVEL SECURITY;
ALTER TABLE workflow_executions ENABLE ROW LEVEL SECURITY;

-- Workflow policies
CREATE POLICY workflow_select_policy ON workflows
    FOR SELECT
    USING (basejump.has_role_on_account(account_id) = true);

CREATE POLICY workflow_insert_policy ON workflows
    FOR INSERT
    WITH CHECK (basejump.has_role_on_account(account_id) = true);

CREATE POLICY workflow_update_policy ON workflows
    FOR UPDATE
    USING (basejump.has_role_on_account(account_id) = true);

CREATE POLICY workflow_delete_policy ON workflows
    FOR DELETE
    USING (basejump.has_role_on_account(account_id) = true);

-- Workflow steps policies based on workflow ownership
CREATE POLICY workflow_step_select_policy ON workflow_steps
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM workflows
            WHERE workflows.workflow_id = workflow_steps.workflow_id
            AND basejump.has_role_on_account(workflows.account_id) = true
        )
    );

CREATE POLICY workflow_step_insert_policy ON workflow_steps
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM workflows
            WHERE workflows.workflow_id = workflow_steps.workflow_id
            AND basejump.has_role_on_account(workflows.account_id) = true
        )
    );

CREATE POLICY workflow_step_update_policy ON workflow_steps
    FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM workflows
            WHERE workflows.workflow_id = workflow_steps.workflow_id
            AND basejump.has_role_on_account(workflows.account_id) = true
        )
    );

CREATE POLICY workflow_step_delete_policy ON workflow_steps
    FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM workflows
            WHERE workflows.workflow_id = workflow_steps.workflow_id
            AND basejump.has_role_on_account(workflows.account_id) = true
        )
    );

-- Workflow execution policies based on workflow ownership
CREATE POLICY workflow_execution_select_policy ON workflow_executions
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM workflows
            WHERE workflows.workflow_id = workflow_executions.workflow_id
            AND basejump.has_role_on_account(workflows.account_id) = true
        )
    );

CREATE POLICY workflow_execution_insert_policy ON workflow_executions
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM workflows
            WHERE workflows.workflow_id = workflow_executions.workflow_id
            AND basejump.has_role_on_account(workflows.account_id) = true
        )
    );

CREATE POLICY workflow_execution_update_policy ON workflow_executions
    FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM workflows
            WHERE workflows.workflow_id = workflow_executions.workflow_id
            AND basejump.has_role_on_account(workflows.account_id) = true
        )
    );

CREATE POLICY workflow_execution_delete_policy ON workflow_executions
    FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM workflows
            WHERE workflows.workflow_id = workflow_executions.workflow_id
            AND basejump.has_role_on_account(workflows.account_id) = true
        )
    );
