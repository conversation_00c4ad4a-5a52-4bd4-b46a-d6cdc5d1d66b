"""
Workflow extraction service for automatically generating workflows from chat messages.

This service analyzes chat messages containing XML tool calls and extracts them into
reusable workflows using an OpenAI agent specifically trained for this purpose.
"""

import json
import re
import uuid
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from utils.logger import logger
from services.supabase import DBConnection
from services.llm import make_llm_api_call
from agentpress.response_processor import ResponseProcessor


class WorkflowExtractionService:
    """Service for extracting workflows from chat messages."""

    def __init__(self):
        self.db = DBConnection()
        self.response_processor = ResponseProcessor(None, None)  # For XML parsing

    async def extract_workflows_from_thread(
        self, thread_id: str, account_id: str, model_name: str = "gpt-4o-mini"
    ) -> List[Dict[str, Any]]:
        """
        Extract workflows from a specific thread's messages.

        Args:
            thread_id: The thread to analyze
            account_id: Account ID for permission verification
            model_name: LLM model to use for extraction

        Returns:
            List of extracted workflow dictionaries
        """
        try:
            # Get thread messages
            messages = await self._get_thread_messages(thread_id, account_id)
            if not messages:
                logger.warning(f"No messages found for thread {thread_id}")
                return []

            # Filter for assistant messages with tool calls
            assistant_messages = [
                msg
                for msg in messages
                if msg.get("type") == "assistant"
                and self._has_tool_calls(msg.get("content", ""))
            ]

            if not assistant_messages:
                logger.info(
                    f"No assistant messages with tool calls found in thread {thread_id}"
                )
                return []

            # Prepare messages for OpenAI workflow extraction
            extraction_messages = self._prepare_extraction_messages(assistant_messages)

            # Use OpenAI to extract workflows
            workflow_data = await self._extract_workflows_with_llm(
                extraction_messages, model_name
            )

            # Parse and validate extracted workflows
            workflows = self._parse_workflow_response(
                workflow_data, thread_id, account_id
            )

            logger.info(f"Extracted {len(workflows)} workflows from thread {thread_id}")
            return workflows

        except Exception as e:
            logger.error(f"Error extracting workflows from thread {thread_id}: {e}")
            raise

    async def save_workflow(self, workflow_data: Dict[str, Any]) -> str:
        """
        Save a workflow to the database.

        Args:
            workflow_data: Workflow data including steps

        Returns:
            The workflow_id of the created workflow
        """
        try:
            client = await self.db.client

            # Create workflow record
            workflow_result = (
                await client.table("workflows")
                .insert(
                    {
                        "name": workflow_data["name"],
                        "description": workflow_data["description"],
                        "account_id": workflow_data["account_id"],
                        "source_thread_id": workflow_data.get("source_thread_id"),
                        "status": workflow_data.get("status", "active"),
                        "metadata": workflow_data.get("metadata", {}),
                    }
                )
                .execute()
            )

            if not workflow_result.data:
                raise Exception("Failed to create workflow")

            workflow_id = workflow_result.data[0]["workflow_id"]

            # Create workflow steps
            steps_data = []
            for i, step in enumerate(workflow_data["steps"]):
                steps_data.append(
                    {
                        "workflow_id": workflow_id,
                        "step_order": i + 1,
                        "tool_name": step["tool_name"],
                        "tool_parameters": step["tool_parameters"],
                        "explanation_text": step.get("explanation_text"),
                        "user_context": step.get("user_context"),
                        "parameter_config": step.get("parameter_config", {}),
                    }
                )

            await client.table("workflow_steps").insert(steps_data).execute()

            logger.info(
                f"Successfully saved workflow {workflow_id} with {len(steps_data)} steps"
            )
            return workflow_id

        except Exception as e:
            logger.error(f"Error saving workflow: {e}")
            raise

    async def get_user_workflows(self, account_id: str) -> List[Dict[str, Any]]:
        """
        Get all workflows for a user.

        Args:
            account_id: The user's account ID

        Returns:
            List of workflow dictionaries with steps
        """
        try:
            client = await self.db.client

            # Get workflows
            workflows_result = (
                await client.table("workflows")
                .select("*, workflow_steps(*)")
                .eq("account_id", account_id)
                .order("created_at", desc=True)
                .execute()
            )

            workflows = []
            for workflow in workflows_result.data:
                # Sort steps by order
                steps = sorted(
                    workflow.get("workflow_steps", []), key=lambda x: x["step_order"]
                )
                workflow["steps"] = steps
                del workflow["workflow_steps"]  # Clean up the nested structure
                workflows.append(workflow)

            return workflows

        except Exception as e:
            logger.error(f"Error getting workflows for account {account_id}: {e}")
            raise

    async def delete_workflow(self, workflow_id: str, account_id: str) -> bool:
        """
        Delete a workflow (verifies ownership).

        Args:
            workflow_id: Workflow to delete
            account_id: Account ID for permission verification

        Returns:
            True if deleted successfully
        """
        try:
            client = await self.db.client

            # Verify ownership and delete
            result = (
                await client.table("workflows")
                .delete()
                .eq("workflow_id", workflow_id)
                .eq("account_id", account_id)
                .execute()
            )

            return len(result.data) > 0

        except Exception as e:
            logger.error(f"Error deleting workflow {workflow_id}: {e}")
            raise

    async def _get_thread_messages(
        self, thread_id: str, account_id: str
    ) -> List[Dict[str, Any]]:
        """Get all messages from a thread with permission verification."""
        try:
            client = await self.db.client

            # First verify thread access
            thread_result = (
                await client.table("threads")
                .select("thread_id")
                .eq("thread_id", thread_id)
                .eq("account_id", account_id)
                .execute()
            )

            if not thread_result.data:
                raise Exception(f"Thread {thread_id} not found or access denied")

            # Get messages
            messages_result = (
                await client.table("messages")
                .select("*")
                .eq("thread_id", thread_id)
                .order("created_at", desc=False)
                .execute()
            )

            return messages_result.data

        except Exception as e:
            logger.error(f"Error getting messages for thread {thread_id}: {e}")
            raise

    def _has_tool_calls(self, content: str) -> bool:
        """Check if message content contains XML tool calls."""
        if not content:
            return False

        # Try to parse content as JSON first
        try:
            parsed = json.loads(content)
            content_text = parsed.get("content", content)
        except (json.JSONDecodeError, TypeError):
            content_text = content

        # Check for XML tool call pattern
        xml_pattern = r"<(?!inform\b)([a-zA-Z\-_]+)(?:\s+[^>]*)?>(?:[\s\S]*?)<\/\1>|<(?!inform\b)([a-zA-Z\-_]+)(?:\s+[^>]*)?\/>"
        return bool(re.search(xml_pattern, content_text))

    def _prepare_extraction_messages(
        self, assistant_messages: List[Dict[str, Any]]
    ) -> List[Dict[str, str]]:
        """Prepare messages for OpenAI workflow extraction."""
        # System prompt for workflow extraction
        system_prompt = """You are a workflow extraction expert. Your job is to analyze chat messages containing XML tool calls and extract them into reusable workflows.

TASK: Analyze the provided assistant messages and identify discrete workflows - sequences of tool calls that achieve specific goals.

GUIDELINES:
1. Group related tool calls into logical workflows based on:
   - Shared purpose/goal
   - Sequential dependencies
   - User intent patterns

2. For each workflow, extract:
   - A descriptive name (what the workflow does)
   - A clear description of its purpose
   - The sequence of tool calls in order
   - Context about why each tool was called
   - Which parameters might need to be variables vs. static

3. Identify parameter types:
   - STATIC: Always the same value
   - VARIABLE: User should provide new values when running
   - DYNAMIC: System-generated (timestamps, IDs, etc.)

4. Filter out:
   - Single tool calls that don't form workflows
   - Error corrections or retries
   - Tool calls that are too specific to be reusable

RESPONSE FORMAT: Return a JSON array of workflows:
```json
[
  {
    "name": "Workflow Name",
    "description": "What this workflow accomplishes",
    "steps": [
      {
        "tool_name": "tool-name",
        "tool_parameters": {...},
        "explanation_text": "Why this step is needed",
        "user_context": "User intent for this step",
        "parameter_config": {
          "param_name": {"type": "static|variable|dynamic", "description": "..."}
        }
      }
    ]
  }
]
```

Be selective - only extract workflows that would be genuinely useful to rerun."""

        messages = [{"role": "system", "content": system_prompt}]

        # Add assistant messages as context
        content_parts = []
        for i, msg in enumerate(assistant_messages):
            try:
                parsed = json.loads(msg.get("content", ""))
                content_text = parsed.get("content", msg.get("content", ""))
            except (json.JSONDecodeError, TypeError):
                content_text = msg.get("content", "")

            content_parts.append(
                f"Message {i+1} (ID: {msg.get('message_id', 'unknown')}):\n{content_text}\n"
            )

        messages.append(
            {
                "role": "user",
                "content": f"Extract workflows from these assistant messages:\n\n{''.join(content_parts)}",
            }
        )

        return messages

    async def _extract_workflows_with_llm(
        self, messages: List[Dict[str, str]], model_name: str
    ) -> str:
        """Use OpenAI to extract workflows from messages."""
        try:
            response = await make_llm_api_call(
                messages=messages,
                model_name=model_name,
                temperature=0.1,
                max_tokens=4000,
                response_format={"type": "json_object"},
            )

            if hasattr(response, "choices") and response.choices:
                return response.choices[0].message.content
            elif isinstance(response, dict) and "choices" in response:
                return response["choices"][0]["message"]["content"]
            else:
                raise Exception(f"Unexpected response format: {response}")

        except Exception as e:
            logger.error(f"Error calling LLM for workflow extraction: {e}")
            raise

    def _parse_workflow_response(
        self, response_content: str, source_thread_id: str, account_id: str
    ) -> List[Dict[str, Any]]:
        """Parse and validate the LLM response into workflow objects."""
        try:
            # Parse JSON response
            data = json.loads(response_content)

            # Handle both array and object responses
            if isinstance(data, dict) and "workflows" in data:
                workflows_data = data["workflows"]
            elif isinstance(data, list):
                workflows_data = data
            else:
                logger.warning(f"Unexpected workflow response format: {data}")
                return []

            workflows = []
            for workflow_data in workflows_data:
                # Validate required fields
                if not all(
                    key in workflow_data for key in ["name", "description", "steps"]
                ):
                    logger.warning(f"Skipping invalid workflow: {workflow_data}")
                    continue

                # Validate steps
                valid_steps = []
                for step in workflow_data.get("steps", []):
                    if not all(key in step for key in ["tool_name", "tool_parameters"]):
                        logger.warning(f"Skipping invalid step: {step}")
                        continue
                    valid_steps.append(step)

                if not valid_steps:
                    logger.warning(
                        f"Workflow '{workflow_data['name']}' has no valid steps"
                    )
                    continue

                # Create workflow object
                workflow = {
                    "name": workflow_data["name"],
                    "description": workflow_data["description"],
                    "account_id": account_id,
                    "source_thread_id": source_thread_id,
                    "steps": valid_steps,
                    "metadata": {
                        "extracted_at": datetime.utcnow().isoformat(),
                        "extraction_model": "workflow_extraction_v1",
                    },
                }

                workflows.append(workflow)

            return workflows

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse workflow response as JSON: {e}")
            logger.error(f"Response content: {response_content}")
            return []
        except Exception as e:
            logger.error(f"Error parsing workflow response: {e}")
            return []
