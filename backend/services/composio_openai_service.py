"""
Composio OpenAI Service - Integration with OpenAI function calling

This module provides OpenAI function calling integration with Composio,
allowing dynamic fetching and execution of tool definitions with per-user
credential management.
"""

from composio_openai import ComposioToolSet, Action, App
from typing import Dict, List, Any, Optional, Union
import logging
import os
import json
from supabase import create_client, Client

logger = logging.getLogger(__name__)


class ComposioOpenAIService:
    """
    Service for handling Composio's OpenAI integration with per-user credentials

    This service provides methods for fetching OpenAI-compatible tool
    definitions and handling tool calls from OpenAI completions using
    user-specific entity IDs stored in Supabase.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        supabase_url: Optional[str] = None,
        supabase_key: Optional[str] = None,
    ):
        """
        Initialize the Composio OpenAI service.

        Args:
            api_key: Optional API key for Composio. If not provided,
                    uses the COMPOSIO_API_KEY environment variable.
            supabase_url: Optional Supabase URL. If not provided,
                         uses the SUPABASE_URL environment variable.
            supabase_key: Optional Supabase key. If not provided,
                         uses the SUPABASE_ANON_KEY environment variable.
        """
        self.api_key = api_key or os.getenv("COMPOSIO_API_KEY")
        if not self.api_key:
            raise ValueError("COMPOSIO_API_KEY environment variable is required")

        # Initialize Supabase client for credential retrieval
        self.supabase_url = supabase_url or os.getenv("SUPABASE_URL")
        self.supabase_key = supabase_key or os.getenv("SUPABASE_SERVICE_ROLE_KEY")

        if not self.supabase_url or not self.supabase_key:
            raise ValueError(
                "SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables are required"
            )

        self.supabase: Client = create_client(self.supabase_url, self.supabase_key)

        # Cache for tool definitions to avoid repeated API calls
        self._tools_cache = {}
        # Cache for user entity mappings
        self._entity_cache = {}

    def _get_user_entity_id(self, user_id: str) -> Optional[str]:
        """
        Get the Composio entity ID for a user from the database.

        Args:
            user_id: The user's ID

        Returns:
            The entity ID if found, None otherwise
        """
        # Check cache first
        if user_id in self._entity_cache:
            return self._entity_cache[user_id]

        try:
            # Query the user_mcp_connections table for active connections
            response = (
                self.supabase.table("user_mcp_connections")
                .select("composio_entity_id")
                .eq("user_id", user_id)
                .eq("is_active", True)
                .limit(1)
                .execute()
            )

            if response.data and len(response.data) > 0:
                entity_id = response.data[0]["composio_entity_id"]
                # Cache the result
                self._entity_cache[user_id] = entity_id
                logger.info(f"Found entity ID {entity_id} for user {user_id}")
                return entity_id
            else:
                logger.warning(f"No active Composio entity found for user {user_id}")
                return None

        except Exception as e:
            logger.error(f"Error fetching entity ID for user {user_id}: {e}")
            return None

    def _get_user_active_services(self, user_id: str) -> List[str]:
        """
        Get the list of active services for a user.

        Args:
            user_id: The user's ID

        Returns:
            List of active service names
        """
        try:
            response = (
                self.supabase.table("user_mcp_connections")
                .select("service_name")
                .eq("user_id", user_id)
                .eq("is_active", True)
                .eq("status", "active")
                .execute()
            )

            if response.data:
                services = [row["service_name"] for row in response.data]
                logger.info(
                    f"Found {len(services)} active services for user {user_id}: {services}"
                )
                return services
            else:
                logger.info(f"No active services found for user {user_id}")
                return []

        except Exception as e:
            logger.error(f"Error fetching active services for user {user_id}: {e}")
            return []

    def get_tools(
        self, user_id: str, services: Optional[List[str]] = None
    ) -> List[Dict]:
        """
        Get OpenAI-compatible tool definitions for a user

        Args:
            user_id: User's ID
            services: Optional list of service names to include. If None,
                     uses all active services for the user.

        Returns:
            List of OpenAI tool definitions
        """
        # Get the user's entity ID
        entity_id = self._get_user_entity_id(user_id)
        if not entity_id:
            logger.warning(
                f"No entity ID found for user {user_id}, returning empty tools list"
            )
            return []

        # If no services specified, get all active services for the user
        if services is None:
            services = self._get_user_active_services(user_id)
            if not services:
                logger.info(
                    f"No active services for user {user_id}, returning empty tools list"
                )
                return []

        # Check cache first
        cache_key = f"{entity_id}_{'-'.join(sorted(services))}"
        if cache_key in self._tools_cache:
            logger.info(f"Using cached tool definitions for {cache_key}")
            return self._tools_cache[cache_key]

        try:
            # Initialize toolset with the user's entity_id
            toolset = ComposioToolSet(entity_id=entity_id, api_key=self.api_key)

            # Convert service names to App enums if provided
            apps = None
            if services:
                apps = []
                for service in services:
                    service_lower = service.lower()
                    if service_lower == "gmail":
                        apps.append(App.GMAIL)
                    elif service_lower == "notion":
                        apps.append(App.NOTION)
                    elif service_lower == "github":
                        apps.append(App.GITHUB)
                    elif service_lower == "slack":
                        apps.append(App.SLACK)
                    elif service_lower == "google_drive":
                        apps.append(App.GOOGLEDRIVE)
                    # Add more service mappings as needed
                    else:
                        logger.warning(f"Unknown service: {service}")

            # Get tools for all or specific services
            logger.info(
                f"Fetching OpenAI tools for entity {entity_id} and services {services}"
            )
            tools = toolset.get_tools(apps=apps)
            logger.info(f"Successfully fetched {len(tools)} OpenAI tools")

            # Cache the result
            self._tools_cache[cache_key] = tools

            return tools
        except Exception as e:
            logger.error(f"Error fetching OpenAI tools for user {user_id}: {e}")
            # Return empty list on error to avoid breaking the agent
            return []

    def handle_tool_calls(self, user_id: str, response) -> List[Dict]:
        """
        Execute OpenAI tool calls via Composio

        Args:
            user_id: User's ID
            response: Full OpenAI completion response object

        Returns:
            List of tool execution results
        """
        # Extract tool calls from response
        tool_calls = []
        if hasattr(response, "choices") and response.choices:
            choice = response.choices[0]
            if (
                hasattr(choice, "message")
                and hasattr(choice.message, "tool_calls")
                and choice.message.tool_calls
            ):
                tool_calls = choice.message.tool_calls

        if not tool_calls:
            logger.info("No tool calls found in response")
            return []

        # Get the user's entity ID
        entity_id = self._get_user_entity_id(user_id)
        if not entity_id:
            logger.error(
                f"No entity ID found for user {user_id}, cannot execute tool calls"
            )
            return [
                {"error": f"No entity ID found for user {user_id}"} for _ in tool_calls
            ]

        try:
            # Initialize toolset with the user's entity_id
            toolset = ComposioToolSet(entity_id=entity_id, api_key=self.api_key)

            # Log the tool calls for debugging
            logger.info(
                f"Handling {len(tool_calls)} OpenAI tool calls for entity {entity_id}"
            )
            for call in tool_calls:
                logger.info(
                    f"Tool call: {call.function.name if hasattr(call, 'function') else 'unknown'}"
                )

            # Handle the tool calls (Composio expects full response object)
            results = toolset.handle_tool_calls(response)
            logger.info(f"Successfully executed {len(results)} tool calls")

            return results
        except Exception as e:
            logger.error(f"Error handling OpenAI tool calls for user {user_id}: {e}")
            # Return error response to avoid breaking the agent
            return [{"error": str(e)} for _ in tool_calls]

    @staticmethod
    def from_env():
        """Create an instance from environment variables."""
        return ComposioOpenAIService(
            api_key=os.getenv("COMPOSIO_API_KEY"),
            supabase_url=os.getenv("SUPABASE_URL"),
            supabase_key=os.getenv("SUPABASE_SERVICE_ROLE_KEY"),
        )
