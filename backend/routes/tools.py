from fastapi import APIRouter, HTTPException
from agentpress.tool_registry import ToolRegistry
from typing import List, Dict, Any
from fastapi.responses import JSONResponse
import logging

router = APIRouter()
logger = logging.getLogger(__name__)

# Create a singleton instance
_tool_registry = None


def get_tool_registry():
    global _tool_registry
    if _tool_registry is None:
        logger.info("Initializing ToolRegistry singleton")
        _tool_registry = ToolRegistry()
        # Log the number of tools loaded
        logger.info(f"ToolRegistry initialized with {len(_tool_registry._tools)} tools")
        # Log each tool's name and schemas
        for func_name, tool_info in _tool_registry._tools.items():
            logger.info(f"Loaded tool function: {func_name}")
            if "schema" in tool_info:
                logger.info(f"  - Has OpenAPI schema: {tool_info['schema'].schema}")
    return _tool_registry


@router.get("/tools", response_model=List[Dict[str, Any]])
async def get_available_tools():
    """Get all available tools and their schemas."""
    try:
        # Get singleton instance
        tool_registry = get_tool_registry()

        # Get OpenAPI schemas
        schemas = tool_registry.get_openapi_schemas()
        logger.info(f"Retrieved {len(schemas)} OpenAPI schemas")

        # Convert schemas to frontend-friendly format
        tools_list = []
        for schema in schemas:
            if schema.get("type") == "function" and "function" in schema:
                function_info = schema["function"]
                tool = {
                    "name": function_info.get("name", "unknown"),
                    "description": function_info.get(
                        "description", "No description available"
                    ),
                    "type": schema.get("type", "function"),
                    "parameters": function_info.get("parameters", {}),
                }
                tools_list.append(tool)
                logger.debug(f"Added tool to response: {tool['name']}")

        logger.info(f"Returning {len(tools_list)} tools")
        return tools_list

    except Exception as e:
        logger.error(f"Error getting available tools: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
