from pydantic import BaseModel
from typing import List, Optional


class ComposioToolActionInfo(BaseModel):
    id: str  # e.g., GMAIL_SEND_AN_EMAIL
    name: str  # User-friendly name, can be derived from id or schema
    description: Optional[str] = None
    app_name: str  # e.g., "Gmail"
    # parameters_schema: Optional[dict] = None # Optionally include the parameter schema for frontend display if needed


class ComposioAppIntegrationInfo(BaseModel):
    app_name: str  # e.g., "Gmail", "GitHub"
    app_enum_value: str  # e.g., "GMAIL", "GITHUB" (from composio_openai.App)
    connected: bool
    actions: List[ComposioToolActionInfo] = []
    error_message: Optional[str] = (
        None  # If connection/tool fetching failed for this app
    )
