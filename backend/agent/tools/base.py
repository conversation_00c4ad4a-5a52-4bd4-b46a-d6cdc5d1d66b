from typing import Dict, List, Any, Optional
from agentpress.tool import (
    Tool as AgentpressTool,
    ToolResult,
    openapi_schema,
    xml_schema,
    ToolSchema,
    SchemaType,
)
import logging
import inspect

logger = logging.getLogger(__name__)


class Tool(AgentpressTool):
    """Base class for all tools in the agent system.

    Inherits from agentpress.tool.Tool to ensure proper schema registration
    and tool discovery.
    """

    def __init__(self):
        """Initialize the tool."""
        super().__init__()
        self._schemas: Dict[str, List[ToolSchema]] = {}
        logger.debug(f"Initialized {self.__class__.__name__}")
        self._register_schemas()

    def _register_schemas(self):
        """Register schemas from all decorated methods."""
        for name, method in inspect.getmembers(self, predicate=inspect.ismethod):
            if hasattr(method, "tool_schemas"):
                self._schemas[name] = method.tool_schemas
                logger.debug(
                    f"Registered schemas for method '{name}' in {self.__class__.__name__}"
                )

    def get_schemas(self) -> Dict[str, List[ToolSchema]]:
        """Get all registered schemas for this tool.

        Returns:
            Dict mapping function names to their schemas
        """
        return self._schemas

    def success_response(self, data: Any) -> ToolResult:
        """Create a successful tool result."""
        return ToolResult(success=True, output=data)

    def fail_response(self, error: str) -> ToolResult:
        """Create a failed tool result."""
        return ToolResult(success=False, output=error)
